^D:\COMPANY\NVIDIA_DLSS\STREAMLINE\_ARTIFACTS\SL.INTERPOSER\DEBUG_X64\D3D11.OBJ|D:\COMPANY\NVIDIA_DLSS\STREAMLINE\_ARTIFACTS\SL.INTERPOSER\DEBUG_X64\D3D12.OBJ|D:\COMPANY\NVIDIA_DLSS\STREAMLINE\_ARTIFACTS\SL.INTERPOSER\DEBUG_X64\D3D12COMMANDLIST.OBJ|D:\COMPANY\NVIDIA_DLSS\STREAMLINE\_ARTIFACTS\SL.INTERPOSER\DEBUG_X64\D3D12COMMANDQUEUE.OBJ|D:\COMPANY\NVIDIA_DLSS\STREAMLINE\_ARTIFACTS\SL.INTERPOSER\DEBUG_X64\D3D12DEVICE.OBJ|D:\COMPANY\NVIDIA_DLSS\STREAMLINE\_ARTIFACTS\SL.INTERPOSER\DEBUG_X64\DXGI.OBJ|D:\COMPANY\NVIDIA_DLSS\STREAMLINE\_ARTIFACTS\SL.INTERPOSER\DEBUG_X64\DXGIFACTORY.OBJ|D:\COMPANY\NVIDIA_DLSS\STREAMLINE\_ARTIFACTS\SL.INTERPOSER\DEBUG_X64\DXGISWAPCHAIN.OBJ|D:\COMPANY\NVIDIA_DLSS\STREAMLINE\_ARTIFACTS\SL.INTERPOSER\DEBUG_X64\EXCEPTION.OBJ|D:\COMPANY\NVIDIA_DLSS\STREAMLINE\_ARTIFACTS\SL.INTERPOSER\DEBUG_X64\HOOK.OBJ|D:\COMPANY\NVIDIA_DLSS\STREAMLINE\_ARTIFACTS\SL.INTERPOSER\DEBUG_X64\INTERPOSER.RES|D:\COMPANY\NVIDIA_DLSS\STREAMLINE\_ARTIFACTS\SL.INTERPOSER\DEBUG_X64\LOG.OBJ|D:\COMPANY\NVIDIA_DLSS\STREAMLINE\_ARTIFACTS\SL.INTERPOSER\DEBUG_X64\OTA.OBJ|D:\COMPANY\NVIDIA_DLSS\STREAMLINE\_ARTIFACTS\SL.INTERPOSER\DEBUG_X64\PARAMETERS.OBJ|D:\COMPANY\NVIDIA_DLSS\STREAMLINE\_ARTIFACTS\SL.INTERPOSER\DEBUG_X64\PLUGINMANAGER.OBJ|D:\COMPANY\NVIDIA_DLSS\STREAMLINE\_ARTIFACTS\SL.INTERPOSER\DEBUG_X64\SECURELOADLIBRARY.OBJ|D:\COMPANY\NVIDIA_DLSS\STREAMLINE\_ARTIFACTS\SL.INTERPOSER\DEBUG_X64\SL.OBJ|D:\COMPANY\NVIDIA_DLSS\STREAMLINE\_ARTIFACTS\SL.INTERPOSER\DEBUG_X64\WRAPPER.OBJ
D:\Company\NVIDIA_DLSS\Streamline\_artifacts\sl.interposer\Debug_x64\sl.interposer.lib
D:\Company\NVIDIA_DLSS\Streamline\_artifacts\sl.interposer\Debug_x64\sl.interposer.EXP
D:\Company\NVIDIA_DLSS\Streamline\_artifacts\sl.interposer\Debug_x64\sl.interposer.ilk
