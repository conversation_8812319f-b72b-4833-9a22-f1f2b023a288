# DLSS Demo 快速启动指南

## 🚀 快速开始

### 第一步：获取必要文件

您需要从NVIDIA获取以下文件：

1. **Streamline SDK DLLs**
   - `sl.interposer.dll`
   - `sl.common.dll` 
   - `sl.dlss.dll`

2. **DLSS Runtime**
   - `nvngx_dlss.dll`

3. **配置文件**
   - `sl.interposer.json`
   - `sl.common.json`

### 第二步：文件放置

将文件放置到以下位置：
```
Streamline/bin/x64/
├── sl.interposer.dll
├── sl.common.dll
├── sl.dlss.dll
├── nvngx_dlss.dll
├── sl.interposer.json
└── sl.common.json
```

### 第三步：构建Demo

```bash
cd Streamline/demo
build_demo.bat
```

### 第四步：运行Demo

```bash
cd build/bin/Debug
SimpleDLSSDemo.exe
```

## 📁 文件获取方法

### 方法1：下载预编译SDK（推荐）

1. 访问 [NVIDIA Developer](https://developer.nvidia.com/rtx/streamline)
2. 下载 "Streamline SDK"
3. 解压并复制DLL文件到 `Streamline/bin/x64/`

### 方法2：从DLSS SDK获取

1. 访问 [DLSS SDK](https://developer.nvidia.com/rtx/dlss)
2. 下载DLSS SDK
3. 复制 `nvngx_dlss.dll` 到 `Streamline/bin/x64/`

### 方法3：从游戏安装目录复制

某些支持DLSS的游戏包含这些DLL文件，可以从游戏目录复制。

## 🔧 构建要求

- Windows 10/11 64位
- Visual Studio 2022
- CMake 3.16+
- NVIDIA RTX GPU

## 📋 验证清单

在运行demo之前，请确认：

- [ ] RTX GPU已安装
- [ ] 最新NVIDIA驱动程序
- [ ] Visual Studio 2022已安装
- [ ] 所有DLL文件在正确位置
- [ ] JSON配置文件存在

## 🎯 Demo说明

### 简单Demo (SimpleDLSSDemo.exe)
- 控制台应用程序
- 展示基本DLSS初始化
- 适合学习API使用

### 高级Demo (AdvancedDLSSDemo.exe)  
- 窗口应用程序
- 完整D3D12集成
- 展示实际DLSS使用

## ⚠️ 常见问题

### "DLSS not supported"
- 确认使用RTX GPU
- 更新NVIDIA驱动程序

### "DLL not found"
- 检查文件路径
- 确认DLL版本匹配

### 构建失败
- 确认Visual Studio 2022
- 检查CMake版本

## 📞 获取帮助

1. 查看 `README.md` 详细文档
2. 检查 `DEMO_STATUS.md` 状态报告
3. 访问NVIDIA开发者论坛

---

**快速启动完成后，您将看到DLSS功能演示！** 🎉
