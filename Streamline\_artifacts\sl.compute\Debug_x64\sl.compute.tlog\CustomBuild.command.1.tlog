^D:\COMPANY\NVIDIA_DLSS\STREAMLINE\SHADERS\COPY.HLSL
D:\Company\NVIDIA_DLSS\Streamline\external\slang\bin\windows-x64\release\slangc "..\..\shaders\copy.hlsl" -entry main -target spirv -o "D:/Company/NVIDIA_DLSS/Streamline/_artifacts/shaders/copy.spv"
D:\Company\NVIDIA_DLSS\Streamline\external\slang\bin\windows-x64\release\slangc "..\..\shaders\copy.hlsl" -profile sm_5_0 -entry main -target dxbc -o "D:/Company/NVIDIA_DLSS/Streamline/_artifacts/shaders/copy.cs"
pushd D:\Company\NVIDIA_DLSS\Streamline\_artifacts\shaders\
powershell.exe -NoProfile -ExecutionPolicy Bypass D:\Company\NVIDIA_DLSS\Streamline\tools\bin2cheader.ps1 -i "copy.spv" > "copy_spv.h"
powershell.exe -NoProfile -ExecutionPolicy Bypass D:\Company\NVIDIA_DLSS\Streamline\tools\bin2cheader.ps1 -i "copy.cs" > "copy_cs.h"
popd
^D:\COMPANY\NVIDIA_DLSS\STREAMLINE\SHADERS\COPY_TO_BUFFER.HLSL
D:\Company\NVIDIA_DLSS\Streamline\external\slang\bin\windows-x64\release\slangc "..\..\shaders\copy_to_buffer.hlsl" -entry main -target spirv -o "D:/Company/NVIDIA_DLSS/Streamline/_artifacts/shaders/copy_to_buffer.spv"
D:\Company\NVIDIA_DLSS\Streamline\external\slang\bin\windows-x64\release\slangc "..\..\shaders\copy_to_buffer.hlsl" -profile sm_5_0 -entry main -target dxbc -o "D:/Company/NVIDIA_DLSS/Streamline/_artifacts/shaders/copy_to_buffer.cs"
pushd D:\Company\NVIDIA_DLSS\Streamline\_artifacts\shaders\
powershell.exe -NoProfile -ExecutionPolicy Bypass D:\Company\NVIDIA_DLSS\Streamline\tools\bin2cheader.ps1 -i "copy_to_buffer.spv" > "copy_to_buffer_spv.h"
powershell.exe -NoProfile -ExecutionPolicy Bypass D:\Company\NVIDIA_DLSS\Streamline\tools\bin2cheader.ps1 -i "copy_to_buffer.cs" > "copy_to_buffer_cs.h"
popd
^D:\COMPANY\NVIDIA_DLSS\STREAMLINE\SHADERS\MVEC.HLSL
D:\Company\NVIDIA_DLSS\Streamline\external\slang\bin\windows-x64\release\slangc "..\..\shaders\mvec.hlsl" -entry main -target spirv -o "D:/Company/NVIDIA_DLSS/Streamline/_artifacts/shaders/mvec.spv"
D:\Company\NVIDIA_DLSS\Streamline\external\slang\bin\windows-x64\release\slangc "..\..\shaders\mvec.hlsl" -profile sm_5_0 -entry main -target dxbc -o "D:/Company/NVIDIA_DLSS/Streamline/_artifacts/shaders/mvec.cs"
pushd D:\Company\NVIDIA_DLSS\Streamline\_artifacts\shaders\
powershell.exe -NoProfile -ExecutionPolicy Bypass D:\Company\NVIDIA_DLSS\Streamline\tools\bin2cheader.ps1 -i "mvec.spv" > "mvec_spv.h"
powershell.exe -NoProfile -ExecutionPolicy Bypass D:\Company\NVIDIA_DLSS\Streamline\tools\bin2cheader.ps1 -i "mvec.cs" > "mvec_cs.h"
popd
