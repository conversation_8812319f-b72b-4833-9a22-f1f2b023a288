#!/usr/bin/env python3
"""
DLSS视频补帧项目安装脚本
自动安装依赖和设置环境
"""

import os
import sys
import subprocess
import platform
import shutil
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    print(f"Python版本检查通过: {sys.version}")
    return True

def check_cuda():
    """检查CUDA环境"""
    try:
        import torch
        if torch.cuda.is_available():
            cuda_version = torch.version.cuda
            gpu_count = torch.cuda.device_count()
            print(f"CUDA检查通过: 版本 {cuda_version}, GPU数量: {gpu_count}")
            
            for i in range(gpu_count):
                gpu_name = torch.cuda.get_device_name(i)
                print(f"  GPU {i}: {gpu_name}")
            return True
        else:
            print("警告: CUDA不可用，将使用CPU处理（性能较低）")
            return False
    except ImportError:
        print("警告: PyTorch未安装，无法检查CUDA")
        return False

def install_requirements():
    """安装依赖包"""
    print("正在安装依赖包...")
    
    requirements_file = "requirements.txt"
    if not os.path.exists(requirements_file):
        print(f"错误: 找不到 {requirements_file}")
        return False
    
    try:
        # 升级pip
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # 安装依赖
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", requirements_file])
        
        print("依赖包安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"安装依赖包失败: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    directories = [
        "input_videos",
        "output_videos", 
        "temp",
        "logs",
        "models"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"创建目录: {directory}")

def download_dlss_sdk():
    """下载DLSS SDK（需要用户手动操作）"""
    print("\n=== DLSS SDK 安装说明 ===")
    print("1. 访问 NVIDIA 开发者网站: https://developer.nvidia.com/")
    print("2. 注册/登录开发者账号")
    print("3. 下载 NVIDIA NGX SDK")
    print("4. 将 nvngx_dlss.dll 复制到项目根目录或 libs/ 目录")
    print("5. 或者将DLL路径添加到系统PATH环境变量")
    print("\n注意: DLSS SDK需要NVIDIA开发者账号才能下载")

def create_test_script():
    """创建测试脚本"""
    test_script = '''#!/usr/bin/env python3
"""
DLSS视频补帧测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dlss_video_interpolation import check_system_requirements
from dlss_integration import DLSSIntegration
from config import get_config

def test_basic_functionality():
    """测试基本功能"""
    print("=== 基本功能测试 ===")
    
    # 检查系统要求
    check_system_requirements()
    
    # 测试DLSS集成
    dlss = DLSSIntegration()
    print(f"DLSS集成状态: {'已初始化' if dlss.initialized else '未初始化'}")
    
    # 测试配置
    config = get_config()
    print("配置加载成功")
    
    print("基本功能测试完成")

def test_frame_interpolation():
    """测试帧插值功能"""
    print("\\n=== 帧插值测试 ===")
    
    try:
        import torch
        import numpy as np
        
        # 创建测试帧
        frame1 = torch.randn(1, 3, 1080, 1920)
        frame2 = torch.randn(1, 3, 1080, 1920)
        
        from dlss_integration import DLSSFrameInterpolator
        
        interpolator = DLSSFrameInterpolator(device='cuda' if torch.cuda.is_available() else 'cpu')
        
        # 测试插值
        interpolated = interpolator.interpolate_frame(frame1, frame2, 0.5)
        print(f"插值测试成功，输出形状: {interpolated.shape}")
        
        interpolator.cleanup()
        
    except Exception as e:
        print(f"帧插值测试失败: {e}")

if __name__ == '__main__':
    test_basic_functionality()
    test_frame_interpolation()
    print("\\n所有测试完成！")
'''
    
    with open("test_dlss.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    print("创建测试脚本: test_dlss.py")

def create_batch_script():
    """创建批处理脚本"""
    if platform.system() == "Windows":
        batch_script = '''@echo off
echo DLSS视频补帧工具
echo.

if "%1"=="" (
    echo 使用方法: run_dlss.bat input_video.mp4 output_video.mp4 [multiplier] [quality]
    echo.
    echo 参数说明:
    echo   input_video.mp4  - 输入视频文件
    echo   output_video.mp4 - 输出视频文件
    echo   multiplier       - 帧率倍数 (2, 3, 4) 默认: 2
    echo   quality          - 质量设置 (performance, balanced, quality) 默认: balanced
    echo.
    echo 示例:
    echo   run_dlss.bat input.mp4 output.mp4 2 quality
    pause
    exit /b
)

set INPUT_VIDEO=%1
set OUTPUT_VIDEO=%2
set MULTIPLIER=%3
set QUALITY=%4

if "%MULTIPLIER%"=="" set MULTIPLIER=2
if "%QUALITY%"=="" set QUALITY=balanced

echo 输入视频: %INPUT_VIDEO%
echo 输出视频: %OUTPUT_VIDEO%
echo 帧率倍数: %MULTIPLIER%
echo 质量设置: %QUALITY%
echo.

python dlss_video_interpolation.py --input "%INPUT_VIDEO%" --output "%OUTPUT_VIDEO%" --multiplier %MULTIPLIER% --quality %QUALITY%

echo.
echo 处理完成！
pause
'''
        
        with open("run_dlss.bat", "w", encoding="utf-8") as f:
            f.write(batch_script)
        
        print("创建批处理脚本: run_dlss.bat")

def main():
    """主安装函数"""
    print("=== DLSS视频补帧项目安装程序 ===\n")
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 安装依赖
    if not install_requirements():
        return False
    
    # 创建目录
    create_directories()
    
    # 检查CUDA
    check_cuda()
    
    # 创建测试脚本
    create_test_script()
    
    # 创建批处理脚本
    create_batch_script()
    
    # DLSS SDK说明
    download_dlss_sdk()
    
    print("\n=== 安装完成 ===")
    print("下一步:")
    print("1. 下载并安装DLSS SDK")
    print("2. 运行测试: python test_dlss.py")
    print("3. 准备测试视频文件")
    print("4. 运行示例: python example_usage.py")
    print("5. 或使用批处理脚本: run_dlss.bat input.mp4 output.mp4")
    
    return True

if __name__ == '__main__':
    success = main()
    if not success:
        sys.exit(1) 