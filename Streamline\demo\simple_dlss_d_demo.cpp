/*
* Simple NVIDIA DLSS-D Demo Application
* 
* This demo shows basic DLSS-D initialization and usage.
* DLSS-D is designed for denoising ray-traced effects.
*/

#include <iostream>
#include <windows.h>

// Streamline headers
#include "../include/sl.h"
#include "../include/sl_dlss_d.h"

// Simple log callback
void LogCallback(sl::LogType type, const char* message) {
    const char* typeStr = "UNKNOWN";
    switch (type) {
        case sl::LogType::eInfo: typeStr = "INFO"; break;
        case sl::LogType::eWarn: typeStr = "WARN"; break;
        case sl::LogType::eError: typeStr = "ERROR"; break;
    }
    std::cout << "[SL " << typeStr << "] " << message << std::endl;
}

class SimpleDLSSDDemo {
private:
    bool m_initialized = false;
    
public:
    SimpleDLSSDDemo() = default;
    ~SimpleDLSSDDemo() { Cleanup(); }
    
    bool Initialize();
    bool CheckDLSSDSupport();
    void Cleanup();
};

bool SimpleDLSSDDemo::Initialize() {
    std::cout << "=== Simple DLSS-D Demo ===" << std::endl;
    std::cout << "Initializing Streamline SDK..." << std::endl;
    
    // Set up Streamline preferences
    sl::Preferences pref = {};
    pref.showConsole = true;
    pref.logLevel = sl::LogLevel::eDefault;
    pref.pathsToPlugins = nullptr;
    pref.numPathsToPlugins = 0;
    pref.pathToLogsAndData = nullptr;
    pref.logMessageCallback = LogCallback;
    pref.applicationId = 0; // You would get this from NVIDIA
    pref.projectId = "SimpleDLSSDDemo";
    
    // Initialize Streamline
    sl::Result result = slInit(pref);
    if (result != sl::Result::eOk) {
        std::cerr << "Failed to initialize Streamline: " << (int)result << std::endl;
        return false;
    }
    
    std::cout << "Streamline initialized successfully!" << std::endl;
    m_initialized = true;
    return true;
}

bool SimpleDLSSDDemo::CheckDLSSDSupport() {
    if (!m_initialized) {
        std::cerr << "Streamline not initialized!" << std::endl;
        return false;
    }
    
    std::cout << "Checking DLSS-D support..." << std::endl;
    
    // Check if DLSS-D (DLSS Ray Reconstruction) is supported
    sl::AdapterInfo adapterInfo = {};
    sl::Result result = slIsFeatureSupported(sl::kFeatureDLSS_RR, adapterInfo);
    
    if (result == sl::Result::eOk) {
        std::cout << "✓ DLSS-D is supported on this system!" << std::endl;
        std::cout << "DLSS-D can be used for denoising ray-traced effects." << std::endl;
        return true;
    } else {
        std::cout << "✗ DLSS-D is not supported on this system." << std::endl;
        std::cout << "Reason code: " << (int)result << std::endl;
        std::cout << "Requirements:" << std::endl;
        std::cout << "- NVIDIA RTX GPU (RTX 20/30/40 series)" << std::endl;
        std::cout << "- Latest NVIDIA drivers" << std::endl;
        std::cout << "- DLSS-D runtime DLLs" << std::endl;
        return false;
    }
}

void SimpleDLSSDDemo::Cleanup() {
    if (m_initialized) {
        std::cout << "Shutting down Streamline..." << std::endl;
        slShutdown();
        m_initialized = false;
    }
}

int main() {
    std::cout << "========================================" << std::endl;
    std::cout << "    NVIDIA DLSS-D Simple Demo" << std::endl;
    std::cout << "========================================" << std::endl;
    std::cout << std::endl;
    
    std::cout << "DLSS-D (DLSS Denoiser) is designed for:" << std::endl;
    std::cout << "• Ray-traced reflections denoising" << std::endl;
    std::cout << "• Ray-traced global illumination denoising" << std::endl;
    std::cout << "• Ray-traced shadows denoising" << std::endl;
    std::cout << "• Other ray-traced effects denoising" << std::endl;
    std::cout << std::endl;
    
    SimpleDLSSDDemo demo;
    
    // Initialize Streamline
    if (!demo.Initialize()) {
        std::cerr << "Failed to initialize demo" << std::endl;
        std::cout << std::endl;
        std::cout << "Troubleshooting:" << std::endl;
        std::cout << "1. Ensure sl.interposer.dll is in the same directory" << std::endl;
        std::cout << "2. Ensure sl.dlss_d.dll is available" << std::endl;
        std::cout << "3. Check that you have an NVIDIA RTX GPU" << std::endl;
        std::cout << "4. Update to the latest NVIDIA drivers" << std::endl;
        return -1;
    }
    
    // Check DLSS-D support
    bool supported = demo.CheckDLSSDSupport();
    
    std::cout << std::endl;
    std::cout << "========================================" << std::endl;
    
    if (supported) {
        std::cout << "    DLSS-D Demo Completed Successfully!" << std::endl;
        std::cout << "========================================" << std::endl;
        std::cout << std::endl;
        std::cout << "Next steps:" << std::endl;
        std::cout << "1. Integrate DLSS-D into your ray tracing pipeline" << std::endl;
        std::cout << "2. Provide required G-buffer inputs (albedo, normals, depth)" << std::endl;
        std::cout << "3. Supply noisy ray-traced color and motion vectors" << std::endl;
        std::cout << "4. Call DLSS-D evaluation to get denoised output" << std::endl;
        std::cout << std::endl;
        std::cout << "For detailed integration guide, see:" << std::endl;
        std::cout << "- DLSS_D_README.md" << std::endl;
        std::cout << "- Streamline Programming Guide" << std::endl;
    } else {
        std::cout << "    DLSS-D Not Available" << std::endl;
        std::cout << "========================================" << std::endl;
        std::cout << std::endl;
        std::cout << "To enable DLSS-D:" << std::endl;
        std::cout << "1. Ensure you have an RTX GPU" << std::endl;
        std::cout << "2. Update NVIDIA drivers" << std::endl;
        std::cout << "3. Verify DLSS-D DLL is present" << std::endl;
    }
    
    std::cout << std::endl;
    std::cout << "Demo completed. Press Enter to exit..." << std::endl;
    std::cin.get();
    
    return supported ? 0 : 1;
}
