d:\company\nvidia_dlss\streamline\_artifacts\sl.interposer\debug_x64\d3d12commandlist.obj
d:\company\nvidia_dlss\streamline\_artifacts\sl.interposer\debug_x64\dxgi.obj
d:\company\nvidia_dlss\streamline\_artifacts\sl.interposer\debug_x64\wrapper.obj
d:\company\nvidia_dlss\streamline\_artifacts\sl.interposer\debug_x64\pluginmanager.obj
d:\company\nvidia_dlss\streamline\_artifacts\sl.interposer\debug_x64\secureloadlibrary.obj
d:\company\nvidia_dlss\streamline\_artifacts\sl.interposer\debug_x64\exception.obj
d:\company\nvidia_dlss\streamline\_artifacts\sl.interposer\debug_x64\log.obj
d:\company\nvidia_dlss\streamline\_artifacts\sl.interposer\debug_x64\dxgiswapchain.obj
d:\company\nvidia_dlss\streamline\_artifacts\sl.interposer\debug_x64\hook.obj
d:\company\nvidia_dlss\streamline\_artifacts\sl.interposer\debug_x64\dxgifactory.obj
d:\company\nvidia_dlss\streamline\_artifacts\sl.interposer\debug_x64\sl.obj
d:\company\nvidia_dlss\streamline\_artifacts\sl.interposer\debug_x64\d3d12device.obj
d:\company\nvidia_dlss\streamline\_artifacts\sl.interposer\debug_x64\d3d12.obj
d:\company\nvidia_dlss\streamline\_artifacts\sl.interposer\debug_x64\d3d11.obj
d:\company\nvidia_dlss\streamline\_artifacts\sl.interposer\debug_x64\d3d12commandqueue.obj
d:\company\nvidia_dlss\streamline\_artifacts\sl.interposer\debug_x64\parameters.obj
d:\company\nvidia_dlss\streamline\_artifacts\sl.interposer\debug_x64\vc143.idb
d:\company\nvidia_dlss\streamline\_artifacts\sl.interposer\debug_x64\vc143.pdb
d:\company\nvidia_dlss\streamline\_artifacts\sl.interposer\debug_x64\sl.interposer.tlog\cl.18252.write.1.tlog
d:\company\nvidia_dlss\streamline\_artifacts\sl.interposer\debug_x64\sl.interposer.tlog\cl.command.1.tlog
d:\company\nvidia_dlss\streamline\_artifacts\sl.interposer\debug_x64\sl.interposer.tlog\cl.read.1.tlog
