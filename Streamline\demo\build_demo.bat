@echo off
setlocal

echo ========================================
echo    Simple DLSS Demo Build Script
echo ========================================
echo.

:: Check if we're in the right directory
if not exist "simple_dlss_demo.cpp" (
    echo Error: simple_dlss_demo.cpp not found!
    echo Please run this script from the demo directory.
    pause
    exit /b 1
)

:: Check if Streamline headers exist
if not exist "..\include\sl.h" (
    echo Error: Streamline headers not found!
    echo Please make sure you're in the Streamline\demo directory.
    pause
    exit /b 1
)

:: Create build directory
if not exist "build" mkdir build
cd build

echo Configuring CMake project...
cmake .. -G "Visual Studio 17 2022" -A x64
if errorlevel 1 (
    echo.
    echo Error: CMake configuration failed!
    echo Make sure you have Visual Studio 2022 installed.
    pause
    exit /b 1
)

echo.
echo Building project...
cmake --build . --config Debug
if errorlevel 1 (
    echo.
    echo Error: Build failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo         Build Completed Successfully!
echo ========================================
echo.
echo The demo executable is located at:
echo %CD%\bin\Debug\SimpleDLSSDemo.exe
echo.

:: Check if required DLLs exist
echo Checking for required DLLs...
set "BIN_DIR=%CD%\bin\Debug"

if exist "%BIN_DIR%\sl.interposer.dll" (
    echo [OK] sl.interposer.dll found
) else (
    echo [WARNING] sl.interposer.dll not found
)

if exist "%BIN_DIR%\sl.common.dll" (
    echo [OK] sl.common.dll found
) else (
    echo [WARNING] sl.common.dll not found
)

if exist "%BIN_DIR%\sl.dlss.dll" (
    echo [OK] sl.dlss.dll found
) else (
    echo [WARNING] sl.dlss.dll not found
)

if exist "%BIN_DIR%\nvngx_dlss.dll" (
    echo [OK] nvngx_dlss.dll found
) else (
    echo [WARNING] nvngx_dlss.dll not found
)

echo.
echo ========================================
echo                 Notes
echo ========================================
echo.
echo To run the demo successfully, you need:
echo 1. An NVIDIA RTX GPU with DLSS support
echo 2. Streamline DLLs (from a built Streamline SDK)
echo 3. DLSS runtime DLLs (nvngx_dlss.dll)
echo.
echo If you see warnings about missing DLLs above,
echo you'll need to:
echo 1. Build the full Streamline SDK first, or
echo 2. Download pre-compiled Streamline binaries
echo.
echo To run the demo:
echo cd bin\Debug
echo SimpleDLSSDemo.exe
echo.

pause
