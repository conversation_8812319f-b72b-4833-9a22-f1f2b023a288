# DLSS-D (DLSS Denoiser) Demo

## 概述

DLSS-D是NVIDIA专门为去噪光线追踪效果而设计的AI降噪器。与传统的DLSS超分辨率不同，DLSS-D专注于提高光线追踪渲染的质量，特别是：

- **光线追踪反射** - 去除反射中的噪点
- **光线追踪全局光照** - 改善GI的质量
- **光线追踪阴影** - 减少软阴影中的噪点
- **其他光线追踪效果** - 环境光遮蔽、间接照明等

## 功能特性

### 🎯 专门用途
- **降噪专家**：专为光线追踪降噪设计
- **质量优先**：提高视觉质量而非性能
- **AI驱动**：使用深度学习模型进行智能降噪

### 📊 输入要求
DLSS-D需要以下输入数据：
- **噪声颜色缓冲区** - 光线追踪的原始噪声结果
- **反照率缓冲区** - 表面材质颜色
- **法线缓冲区** - 世界空间法线
- **深度缓冲区** - 线性深度信息
- **运动向量** - 像素运动信息

### 🔧 预设模式
- **Preset D** - 默认变换器模型
- **Preset E** - 最新变换器模型（支持景深引导）
- **其他预设** - 向后兼容性

## Demo应用程序

### DLSSDDemo.exe
这个demo展示了完整的DLSS-D集成：

```cpp
// 主要功能
- Streamline SDK初始化
- DLSS-D支持检测
- 资源创建和管理
- 降噪评估
- 实时渲染循环
```

### 关键代码片段

#### 初始化DLSS-D
```cpp
// 检查支持
sl::FeatureRequirements requirements = {};
sl::Result result = sl::slIsFeatureSupported(sl::kFeatureDLSS_D, requirements);

// 配置选项
sl::DLSSDOptions options = {};
options.mode = sl::eDLSSDModeBalanced;
options.preset = sl::DLSSDPreset::ePresetE;
options.outputWidth = width;
options.outputHeight = height;

sl::slDLSSDSetOptions(sl::ViewportHandle(0), options);
```

#### 评估DLSS-D
```cpp
// 设置输入资源
sl::Resource inputs[] = {
    noisyColorBuffer,    // 噪声输入
    albedoBuffer,        // 反照率
    normalBuffer,        // 法线
    depthBuffer,         // 深度
    motionVectorBuffer,  // 运动向量
    denoisedOutput       // 输出
};

// 执行降噪
sl::slEvaluateFeature(
    sl::kFeatureDLSS_D,
    viewportHandle,
    commandList,
    inputs,
    inputCount
);
```

## 构建和运行

### 前提条件
- NVIDIA RTX GPU（支持光线追踪）
- Windows 10/11 64位
- Visual Studio 2022
- 最新NVIDIA驱动程序

### 构建步骤

1. **确保DLL文件存在**：
   ```
   Streamline/bin/x64/
   ├── sl.interposer.dll
   ├── sl.common.dll
   ├── sl.dlss_d.dll     ← 您已编译的文件
   └── nvngx_dlss.dll    ← 需要从DLSS SDK获取
   ```

2. **构建demo**：
   ```bash
   cd Streamline/demo
   build_demo.bat
   ```

3. **运行demo**：
   ```bash
   cd build/bin/Debug
   DLSSDDemo.exe
   ```

## 使用场景

### 适用情况
✅ **光线追踪渲染** - 有噪声的RT效果  
✅ **实时应用** - 游戏、可视化  
✅ **质量优先** - 追求最佳视觉效果  
✅ **现代GPU** - RTX 20/30/40系列  

### 不适用情况
❌ **传统光栅化** - 没有光线追踪噪声  
❌ **性能优先** - 主要目标是提升帧率  
❌ **低端硬件** - 需要RTX GPU支持  

## 技术细节

### 输入格式要求
- **颜色缓冲区**: `DXGI_FORMAT_R16G16B16A16_FLOAT` (HDR)
- **反照率**: `DXGI_FORMAT_R16G16B16A16_FLOAT`
- **法线**: `DXGI_FORMAT_R16G16B16A16_SNORM` (世界空间)
- **深度**: `DXGI_FORMAT_R32_FLOAT` (线性深度)
- **运动向量**: `DXGI_FORMAT_R16G16_FLOAT`

### 性能考虑
- **GPU内存**: 需要额外的缓冲区存储
- **计算开销**: AI推理需要GPU计算资源
- **带宽**: 多个输入缓冲区增加内存带宽需求

### 质量优化
- **时间稳定性**: 使用运动向量提高时间一致性
- **空间质量**: 利用G-buffer信息改善空间降噪
- **自适应**: 根据场景内容自动调整降噪强度

## 与传统DLSS的区别

| 特性 | DLSS (超分辨率) | DLSS-D (降噪器) |
|------|----------------|-----------------|
| **主要目的** | 提升分辨率和性能 | 改善光线追踪质量 |
| **输入分辨率** | 低分辨率渲染 | 全分辨率渲染 |
| **输出** | 高分辨率图像 | 降噪图像 |
| **性能影响** | 提升帧率 | 轻微性能开销 |
| **使用场景** | 所有渲染管线 | 光线追踪专用 |

## 故障排除

### 常见问题

1. **"DLSS-D not supported"**
   - 确认RTX GPU和最新驱动
   - 检查sl.dlss_d.dll是否存在

2. **"Evaluation failed"**
   - 验证输入缓冲区格式
   - 检查资源状态转换

3. **质量问题**
   - 确保运动向量准确性
   - 验证G-buffer数据质量
   - 尝试不同预设模式

### 调试技巧
- 启用详细日志记录
- 验证每个输入缓冲区的内容
- 使用图形调试工具检查资源

## 扩展建议

1. **集成到现有引擎**
   - 修改光线追踪管线
   - 添加G-buffer生成
   - 实现运动向量计算

2. **性能优化**
   - 异步执行降噪
   - 优化内存布局
   - 实现自适应质量

3. **质量改进**
   - 实现时间抗锯齿
   - 添加自定义权重
   - 优化预设选择

## 相关资源

- [NVIDIA DLSS文档](https://developer.nvidia.com/rtx/dlss)
- [Streamline编程指南](../docs/ProgrammingGuide.md)
- [光线追踪最佳实践](https://developer.nvidia.com/rtx)

---

**DLSS-D让您的光线追踪效果更加清晰美观！** ✨
