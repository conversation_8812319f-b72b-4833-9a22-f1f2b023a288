^D:\COMPANY\NVIDIA_DLSS\STREAMLINE\_ARTIFACTS\SL.DIRECTSR\DEBUG_X64\DIRECTSRENTRY.OBJ|D:\COMPANY\NVIDIA_DLSS\STREAMLINE\_ARTIFACTS\SL.DIRECTSR\DEBUG_X64\DIRECTSR_GUID.OBJ|D:\COMPANY\NVIDIA_DLSS\STREAMLINE\_ARTIFACTS\SL.DIRECTSR\DEBUG_X64\PLUGIN.OBJ|D:\COMPANY\NVIDIA_DLSS\STREAMLINE\_ARTIFACTS\SL.DIRECTSR\DEBUG_X64\PLUGIN.RES|D:\COMPANY\NVIDIA_DLSS\STREAMLINE\_ARTIFACTS\SL.DIRECTSR\DEBUG_X64\SECURELOADLIBRARY.OBJ
D:\Company\NVIDIA_DLSS\Streamline\_artifacts\sl.directsr\Debug_x64\sl.directsr.lib
D:\Company\NVIDIA_DLSS\Streamline\_artifacts\sl.directsr\Debug_x64\sl.directsr.EXP
D:\Company\NVIDIA_DLSS\Streamline\_artifacts\sl.directsr\Debug_x64\sl.directsr.ilk
