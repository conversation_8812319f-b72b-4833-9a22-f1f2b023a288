# Streamline (SL) - 版本 2.8.0

Streamline 是一个开源的跨独立硬件供应商（IHV）解决方案，旨在简化将最新的 NVIDIA 及其他独立硬件供应商的超分辨率技术集成到应用程序和游戏中的过程。该框架允许开发者只需进行一次集成，即可启用多种由硬件供应商支持的超分辨率技术及其他图形效果。

本代码仓库包含了将 Streamline 集成到您应用程序中的 SDK。

如需总体了解，请参阅 [NVIDIA 开发者 Streamline 页面](https://developer.nvidia.com/rtx/streamline)

从 SL 2.0.0 版本开始，除了 DLSS-G 插件外，现在可以重新编译 Streamline 的全部源代码。DLSS-G 插件仅以预编译的 DLL 形式提供。我们同时也为所有其他包含源代码的插件提供了预编译（已签名）的 DLL。大多数应用程序开发者通常无需自行重新构建 Streamline，尤其是在发布版本时；所有用于开发、调试和发布集成 Streamline 及其功能的应用程序所需的组件，均已包含在现有的 `bin/`、`include/` 和 `lib/` 目录中。从源代码编译完全是可选的，可能只对一部分 Streamline 应用开发者感兴趣。如需从源代码构建 Streamline，请参阅以下章节。

------

## 先决条件

#### 硬件

- 支持 DirectX 11 和 Vulkan 1.2 或更高版本的 GPU

#### Windows

- Windows 10 20H1（版本 2004 - 10.0.19041）或更新版本
- 安装最新的显卡驱动程序（**如果使用 NVIDIA 显卡，则必须为 512.15 或更高版本**）
- 安装 VS Code 或 VS2017/VS2019/VS2022，并安装 [SDK 10.0.19041+](https://go.microsoft.com/fwlink/?linkid=2120843)
    - 注意：VS2017 支持已被弃用，将在 SL 2.9 版本中移除
- 安装 "git"。
- 将您的代码仓库分支克隆到本地硬盘，确保在 Windows 上使用 NTFS 磁盘（Streamline 使用符号链接）

## 从源代码构建 Streamline
------------------------------------------------

如本文档开头部分所述，Streamline 现在随附了其大部分源代码，允许希望从本地源代码构建大部分 Streamline 的开发者进行操作。唯一的例外是 DLSS-G 插件，该插件仅提供预编译版本。

> **重要提示：**
> 在发布您的软件时，请仅使用 `production` 构建版本。此外，请使用原始 NVIDIA 签名的 SL DLL，或实现您自己的签名系统（并在 SL 中检查该签名），否则 SL 插件可能会被替换为潜在的恶意模块。

#### 配置和构建项目

Streamline 的所有项目和构建信息（以及所有基于 SL 的应用程序的项目信息）均由一个名为 `premake5.lua` 的独立于平台的构建脚本控制。该文件位于 SL 项目根目录，使用 `premake` 项目创建工具链。任何新项目或对现有项目的更改都必须在此文件中列出。对于大多数项目，项目目录中的所有源文件和头文件将自动添加到该项目中。

要配置一个新的 SL 项目进行构建，有一个名为 `setup.bat` 的脚本。请注意，在 Windows 上，必须从 Windows 命令提示符窗口或 PowerShell 窗口中运行此脚本。

运行 `setup.bat` 脚本将执行以下两项操作：

`setup.bat vs2022`  

1. 使用 NVIDIA 工具 `packman` 将所有构建依赖项拉取到本地机器，并缓存在共享目录中。在 SL 项目中的 `external` 目录与该共享缓存之间创建链接，以指向外部构建依赖项。
2. 运行 `premake` 以在 `_project\vs2017`（适用于 Windows）中生成项目构建文件。

要构建项目，只需在 Visual Studio 中打开 `_project\vs2017\streamline.sln`，选择所需的构建配置并进行构建，或者使用提供的构建脚本：

`./build.bat` 加上 `-{debug|develop|production}`（`debug` 为默认值），或使用 VS IDE 并从 `_project` 目录加载解决方案。

默认设置是针对 x86_64 CPU 架构。

> 注意：构建项目所需的配置极少。任何版本的 Windows 10 均可。然后按照上述说明运行 setup 和 build 脚本即可。具体版本的 Windows、NVIDIA 驱动程序或 Vulkan 都是运行时依赖项，而非编译/链接时依赖项。这使得 SL 可以在零配置的标准虚拟机上构建。这非常方便，请帮助我们保持这一优势。

#### 修改现有项目

请勿直接编辑 MSVC 项目文件（或其他平台上的 Makefile）！始终修改上述的 `premake5.lua` 文件。

当更改现有项目的设置或内容（例如：添加新源文件、更改编译器设置、链接到新库等）时，必须再次运行 `setup.bat`，以使这些更改生效，并且需要在 IDE 中重新加载 MSVC 项目文件或解决方案。

NVIDIA 不建议修改 `include` 目录中的头文件，因为这些更改可能会影响 API 本身，并可能导致开发者构建的组件与 NVIDIA 提供的组件不兼容。

#### 使用本地构建的结果

一旦项目针对某个配置构建完成，生成的未签名 DLL 将位于 `_artifacts\sl.*\<Config>\` 目录中。这些 DLL 可以按需复制到 `bin\x64` 目录，或打包供应用程序本身使用。

显然，`sl.dlss_g.dll` 无法从源代码构建，因此必须使用预构建的副本。

#### （可选）编译着色器

如果您希望重新编译 NIS 插件的着色器，则需要安装 Python 3 并将其添加到系统路径中。

## SDK 打包

- 执行 `./package.bat` 加上 `-{debug|develop|production}`（`production` 为默认值）

生成的打包 SDK 可在 `_sdk` 文件夹中找到。

## 调试

Streamline 提供了多种调试和排查问题的方法。更多信息请参阅以下页面：
* 使用 SL ImGui：[调试 - SL ImGUI（实时数据检查）.md](<docs/Debugging - SL ImGUI (Realtime Data Inspection).md>)
* 使用 JSON 配置文件：[调试 - JSON 配置（插件配置）.md](<docs/Debugging - JSON Configs (Plugin Configs).md>)

### 日志记录

从 Streamline 2.7.x 版本开始，可以强制覆盖日志设置，例如日志级别、日志文件输出路径/名称以及是否将日志输出到单独的控制台窗口。

有两种方式可以覆盖日志设置：通过 Windows 注册表，或使用环境变量。环境变量的优先级高于 Windows 注册表（即，如果同时存在注册表覆盖和环境变量覆盖，将使用环境变量的覆盖值）。

日志路径设置指的是文件系统上的一个可写目录，Streamline 可以在其内放置日志文件（默认名为 `sl.log`）。更改日志名称设置可以修改此日志文件的名称。

日志覆盖键位于 `HKEY_LOCAL_MACHINE\SOFTWARE\NVIDIA Corporation\Global\Streamline` 子键下。

| 描述                                       | 注册表值         | 注册表值类型 | 环境变量        | 有效数据 |
| ---------------------------------------- | -------------- | ---------- | ------------- | ------ |
| 启用控制台日志输出到单独的控制台窗口       | `EnableConsoleLogging` | DWORD      | `SL_ENABLE_CONSOLE_LOGGING` |  [0-1] |
| 日志级别                                   | `LogLevel`     | DWORD      | `SL_LOG_LEVEL`              |  [0-2] |
| 日志文件输出目录                           | `LogPath`      | String     | `SL_LOG_PATH`               |  任意字符串 |
| 日志文件名                                 | `LogName`      | String     | `SL_LOG_NAME`               |  任意字符串 |

为方便起见，Streamline 代码仓库中还提供了两个注册表文件，可以快速启用或禁用日志记录，分别为 `streamline_logging_disable.reg` 和 `streamline_logging_enable.reg`。

> **注意：**
> Streamline 插件程序配置（JSON）的优先级高于这些日志覆盖设置。

## 通用编程指南

请阅读 [ProgrammingGuide.md](docs/ProgrammingGuide.md) 以了解在游戏中的集成方法。

## 高级编程指南 - 手动挂钩以实现最低开销

请阅读 [ProgrammingGuideManualHooking.md](docs/ProgrammingGuideManualHooking.md) 以了解在游戏中的高级 SL 集成方法。

## 按功能划分的编程指南：

- [DLSS 超分辨率](docs/ProgrammingGuideDLSS.md)
- [DLSS 帧生成](docs/ProgrammingGuideDLSS_G.md)
- [Reflex](docs/ProgrammingGuideReflex.md)
- [NIS](docs/ProgrammingGuideNIS.md)

## 示例插件源代码

示例 Streamline 插件源代码位于 [此处](source/plugins/sl.template/templateEntry.cpp)

## 示例应用程序及源代码

使用 Streamline 的示例应用程序可在 [此 Git 仓库](https://github.com/NVIDIA-RTX/Streamline_Sample) 中找到