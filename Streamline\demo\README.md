# Simple DLSS Demo

这是一个简单的DLSS演示应用程序，展示了如何使用NVIDIA Streamline SDK集成DLSS功能。

## 功能特性

- 基本的Streamline SDK初始化
- DLSS支持检测
- DLSS配置和设置
- D3D12渲染管线集成
- 详细的日志输出

## 系统要求

### 硬件要求
- NVIDIA RTX系列显卡（支持DLSS的GPU）
- Windows 10/11 64位

### 软件要求
- Visual Studio 2022（或Visual Studio Build Tools 2022）
- CMake 3.16或更高版本
- Windows SDK

## 构建说明

### 方法1：使用批处理脚本（推荐）

1. 打开命令提示符
2. 导航到demo目录：
   ```cmd
   cd Streamline\demo
   ```
3. 运行构建脚本：
   ```cmd
   build_demo.bat
   ```

### 方法2：手动使用CMake

1. 创建构建目录：
   ```cmd
   mkdir build
   cd build
   ```

2. 配置CMake项目：
   ```cmd
   cmake .. -G "Visual Studio 17 2022" -A x64
   ```

3. 构建项目：
   ```cmd
   cmake --build . --config Debug
   ```

## 运行Demo

### 准备工作

在运行demo之前，您需要确保以下DLL文件位于可执行文件目录中：

#### Streamline核心DLL
- `sl.interposer.dll` - Streamline核心拦截器
- `sl.common.dll` - Streamline通用功能
- `sl.dlss.dll` - DLSS插件

#### DLSS运行时DLL
- `nvngx_dlss.dll` - NVIDIA DLSS运行时

#### 配置文件
- `sl.interposer.json` - Streamline拦截器配置
- `sl.common.json` - 通用配置

### 获取所需文件

这些文件可以通过以下方式获取：

1. **构建完整的Streamline SDK**：
   - 在Streamline根目录运行 `build.bat`
   - 文件将生成在 `_artifacts` 目录中

2. **下载预编译的Streamline SDK**：
   - 从NVIDIA开发者网站下载
   - 解压到适当位置

3. **从DLSS SDK获取**：
   - 下载DLSS SDK
   - 复制相关DLL文件

### 运行

1. 确保所有必需的DLL文件都在可执行文件目录中
2. 运行demo：
   ```cmd
   cd build\bin\Debug
   SimpleDLSSDemo.exe
   ```

## Demo输出

成功运行时，您将看到类似以下的输出：

```
=== Simple DLSS Demo ===
This demo shows basic DLSS integration using Streamline SDK
========================================
Initializing Simple DLSS Demo...
Creating D3D12 device...
Using adapter: NVIDIA GeForce RTX 4080
D3D12 device created successfully
Initializing Streamline...
[SL INFO] Streamline initialized
Streamline initialized successfully
Initializing DLSS...
DLSS is supported!
[SL INFO] DLSS options set successfully
DLSS initialized successfully
Demo initialized successfully!

--- Frame 1 ---
Rendering frame with DLSS...
...
```

## 故障排除

### 常见问题

1. **"DLSS is not supported on this system"**
   - 确保您使用的是支持DLSS的NVIDIA RTX GPU
   - 更新到最新的NVIDIA驱动程序

2. **"Failed to initialize Streamline"**
   - 检查Streamline DLL文件是否存在
   - 确保DLL文件版本兼容

3. **"Failed to create D3D12 device"**
   - 确保您的GPU支持D3D12
   - 检查Windows版本和驱动程序

4. **DLL加载错误**
   - 确保所有依赖的DLL都在同一目录中
   - 检查DLL文件是否为64位版本

### 调试提示

- 启用详细日志记录以获取更多信息
- 使用Visual Studio调试器逐步执行代码
- 检查Windows事件查看器中的错误信息

## 代码结构

```
simple_dlss_demo.cpp
├── SimpleDLSSDemo类
│   ├── Initialize() - 主初始化函数
│   ├── CreateD3D12Device() - 创建D3D12设备
│   ├── InitializeStreamline() - 初始化Streamline SDK
│   ├── InitializeDLSS() - 初始化DLSS功能
│   ├── Render() - 渲染循环
│   └── Cleanup() - 清理资源
└── main() - 程序入口点
```

## 扩展建议

这个基础demo可以扩展为更完整的应用程序：

1. **添加实际的渲染内容**
   - 加载3D模型
   - 实现着色器
   - 添加纹理

2. **实现完整的DLSS管线**
   - 设置输入纹理（颜色、深度、运动向量）
   - 调用DLSS评估
   - 处理输出结果

3. **添加用户界面**
   - DLSS质量设置
   - 性能监控
   - 实时参数调整

4. **性能分析**
   - 帧率测量
   - GPU使用率监控
   - 内存使用分析

## 相关资源

- [NVIDIA Streamline文档](../docs/ProgrammingGuide.md)
- [DLSS编程指南](../docs/ProgrammingGuideDLSS.md)
- [NVIDIA开发者网站](https://developer.nvidia.com/rtx/streamline)
- [DLSS SDK下载](https://developer.nvidia.com/rtx/dlss)

## 许可证

此demo遵循Streamline SDK的许可证条款。请参阅根目录中的license.txt文件。
