/*
* NVIDIA DLSS Streamline Demo Application
* 
* This is a simple demonstration of how to integrate DLSS using Streamline SDK.
* This demo shows the basic initialization and usage patterns for DLSS.
*/

#include <iostream>
#include <windows.h>
#include <d3d12.h>
#include <dxgi1_6.h>
#include <wrl/client.h>

// Streamline headers
#include "../include/sl.h"
#include "../include/sl_dlss.h"
#include "../include/sl_consts.h"

using Microsoft::WRL::ComPtr;

class SimpleDLSSDemo {
private:
    ComPtr<ID3D12Device> m_device;
    ComPtr<IDXGISwapChain3> m_swapChain;
    ComPtr<ID3D12CommandQueue> m_commandQueue;
    ComPtr<ID3D12CommandAllocator> m_commandAllocator;
    ComPtr<ID3D12GraphicsCommandList> m_commandList;
    
    bool m_dlssInitialized = false;
    sl::DLSSOptions m_dlssOptions = {};
    
public:
    SimpleDLSSDemo() = default;
    ~SimpleDLSSDemo() { Cleanup(); }
    
    bool Initialize();
    bool InitializeDLSS();
    void Render();
    void Cleanup();
    
private:
    bool CreateD3D12Device();
    bool InitializeStreamline();
    void LogMessage(sl::LogLevel level, const char* message);
};

// Streamline log callback
void LogCallback(sl::LogLevel level, const char* message) {
    const char* levelStr = "UNKNOWN";
    switch (level) {
        case sl::eLogLevelOff: return; // Don't log anything
        case sl::eLogLevelDefault: levelStr = "DEFAULT"; break;
        case sl::eLogLevelVerbose: levelStr = "VERBOSE"; break;
        case sl::eLogLevelInfo: levelStr = "INFO"; break;
        case sl::eLogLevelWarn: levelStr = "WARN"; break;
        case sl::eLogLevelError: levelStr = "ERROR"; break;
    }
    std::cout << "[SL " << levelStr << "] " << message << std::endl;
}

bool SimpleDLSSDemo::Initialize() {
    std::cout << "Initializing Simple DLSS Demo..." << std::endl;
    
    if (!CreateD3D12Device()) {
        std::cerr << "Failed to create D3D12 device" << std::endl;
        return false;
    }
    
    if (!InitializeStreamline()) {
        std::cerr << "Failed to initialize Streamline" << std::endl;
        return false;
    }
    
    if (!InitializeDLSS()) {
        std::cerr << "Failed to initialize DLSS" << std::endl;
        return false;
    }
    
    std::cout << "Demo initialized successfully!" << std::endl;
    return true;
}

bool SimpleDLSSDemo::CreateD3D12Device() {
    std::cout << "Creating D3D12 device..." << std::endl;
    
    // Enable debug layer in debug builds
#ifdef _DEBUG
    ComPtr<ID3D12Debug> debugController;
    if (SUCCEEDED(D3D12GetDebugInterface(IID_PPV_ARGS(&debugController)))) {
        debugController->EnableDebugLayer();
    }
#endif
    
    // Create DXGI factory
    ComPtr<IDXGIFactory4> factory;
    HRESULT hr = CreateDXGIFactory1(IID_PPV_ARGS(&factory));
    if (FAILED(hr)) {
        std::cerr << "Failed to create DXGI factory" << std::endl;
        return false;
    }
    
    // Find hardware adapter
    ComPtr<IDXGIAdapter1> adapter;
    for (UINT adapterIndex = 0; DXGI_ERROR_NOT_FOUND != factory->EnumAdapters1(adapterIndex, &adapter); ++adapterIndex) {
        DXGI_ADAPTER_DESC1 desc;
        adapter->GetDesc1(&desc);
        
        if (desc.Flags & DXGI_ADAPTER_FLAG_SOFTWARE) {
            continue;
        }
        
        // Try to create device
        hr = D3D12CreateDevice(adapter.Get(), D3D_FEATURE_LEVEL_11_0, IID_PPV_ARGS(&m_device));
        if (SUCCEEDED(hr)) {
            std::wcout << L"Using adapter: " << desc.Description << std::endl;
            break;
        }
    }
    
    if (!m_device) {
        std::cerr << "Failed to create D3D12 device" << std::endl;
        return false;
    }
    
    // Create command queue
    D3D12_COMMAND_QUEUE_DESC queueDesc = {};
    queueDesc.Flags = D3D12_COMMAND_QUEUE_FLAG_NONE;
    queueDesc.Type = D3D12_COMMAND_LIST_TYPE_DIRECT;
    
    hr = m_device->CreateCommandQueue(&queueDesc, IID_PPV_ARGS(&m_commandQueue));
    if (FAILED(hr)) {
        std::cerr << "Failed to create command queue" << std::endl;
        return false;
    }
    
    // Create command allocator
    hr = m_device->CreateCommandAllocator(D3D12_COMMAND_LIST_TYPE_DIRECT, IID_PPV_ARGS(&m_commandAllocator));
    if (FAILED(hr)) {
        std::cerr << "Failed to create command allocator" << std::endl;
        return false;
    }
    
    // Create command list
    hr = m_device->CreateCommandList(0, D3D12_COMMAND_LIST_TYPE_DIRECT, m_commandAllocator.Get(), nullptr, IID_PPV_ARGS(&m_commandList));
    if (FAILED(hr)) {
        std::cerr << "Failed to create command list" << std::endl;
        return false;
    }
    
    m_commandList->Close();
    
    std::cout << "D3D12 device created successfully" << std::endl;
    return true;
}

bool SimpleDLSSDemo::InitializeStreamline() {
    std::cout << "Initializing Streamline..." << std::endl;
    
    // Set up Streamline preferences
    sl::Preferences pref = {};
    pref.showConsole = true; // Show console for debugging
    pref.logLevel = sl::eLogLevelInfo;
    pref.pathsToPlugins = nullptr; // Use default plugin path
    pref.numPathsToPlugins = 0;
    pref.pathToLogsAndData = nullptr; // Use default log path
    pref.logMessageCallback = LogCallback;
    pref.applicationId = 0; // You would get this from NVIDIA
    pref.engineType = sl::eEngineTypeCustom;
    pref.engineVersion = "1.0.0";
    pref.projectId = "SimpleDLSSDemo";
    
    // Initialize Streamline
    sl::Result result = sl::slInit(pref);
    if (result != sl::eOk) {
        std::cerr << "Failed to initialize Streamline: " << (int)result << std::endl;
        return false;
    }
    
    std::cout << "Streamline initialized successfully" << std::endl;
    return true;
}

bool SimpleDLSSDemo::InitializeDLSS() {
    std::cout << "Initializing DLSS..." << std::endl;
    
    // Check if DLSS is supported
    sl::FeatureRequirements requirements = {};
    sl::Result result = sl::slIsFeatureSupported(sl::kFeatureDLSS, requirements);
    if (result != sl::eOk) {
        std::cerr << "DLSS is not supported on this system: " << (int)result << std::endl;
        return false;
    }
    
    std::cout << "DLSS is supported!" << std::endl;
    
    // Set up DLSS options
    m_dlssOptions.mode = sl::eDLSSModeBalanced;
    m_dlssOptions.outputWidth = 1920;
    m_dlssOptions.outputHeight = 1080;
    m_dlssOptions.colorBuffersHDR = false;
    m_dlssOptions.useAutoExposure = false;
    m_dlssOptions.sharpness = 0.0f;
    
    // Set DLSS options
    result = sl::slDLSSSetOptions(sl::ViewportHandle(0), m_dlssOptions);
    if (result != sl::eOk) {
        std::cerr << "Failed to set DLSS options: " << (int)result << std::endl;
        return false;
    }
    
    m_dlssInitialized = true;
    std::cout << "DLSS initialized successfully" << std::endl;
    return true;
}

void SimpleDLSSDemo::Render() {
    if (!m_dlssInitialized) {
        return;
    }
    
    // This is where you would typically:
    // 1. Render your scene at lower resolution
    // 2. Call DLSS to upscale
    // 3. Present the result
    
    std::cout << "Rendering frame with DLSS..." << std::endl;
    
    // Reset command list
    m_commandAllocator->Reset();
    m_commandList->Reset(m_commandAllocator.Get(), nullptr);
    
    // Here you would set up your DLSS inputs and call sl::slEvaluateFeature
    // For this demo, we'll just simulate the process
    
    // Close and execute command list
    m_commandList->Close();
    ID3D12CommandList* ppCommandLists[] = { m_commandList.Get() };
    m_commandQueue->ExecuteCommandLists(_countof(ppCommandLists), ppCommandLists);
}

void SimpleDLSSDemo::Cleanup() {
    std::cout << "Cleaning up..." << std::endl;
    
    if (m_dlssInitialized) {
        // Cleanup DLSS
        sl::slShutdown();
        m_dlssInitialized = false;
    }
    
    // D3D12 objects will be automatically released by ComPtr
    std::cout << "Cleanup complete" << std::endl;
}

int main() {
    std::cout << "=== Simple DLSS Demo ===" << std::endl;
    std::cout << "This demo shows basic DLSS integration using Streamline SDK" << std::endl;
    std::cout << "========================================" << std::endl;
    
    SimpleDLSSDemo demo;
    
    if (!demo.Initialize()) {
        std::cerr << "Failed to initialize demo" << std::endl;
        return -1;
    }
    
    // Simulate a few frames
    for (int i = 0; i < 5; ++i) {
        std::cout << "\n--- Frame " << (i + 1) << " ---" << std::endl;
        demo.Render();
        Sleep(1000); // Wait 1 second between frames
    }
    
    std::cout << "\nDemo completed successfully!" << std::endl;
    return 0;
}
