# NVIDIA DLSS 视频补帧项目

这个项目使用Python实现基于NVIDIA DLSS技术的视频帧插值功能，可以将低帧率视频转换为高帧率视频。

## 功能特性

- 使用NVIDIA DLSS技术进行视频帧插值
- 支持多种视频格式（MP4, AVI, MOV等）
- 可调节插值倍数（2x, 3x, 4x等）
- 实时进度显示
- GPU加速处理

## 系统要求

- NVIDIA GPU（支持DLSS）
- Python 3.8+
- CUDA 11.0+
- Windows 10/11

## 安装

1. 克隆项目：
```bash
git clone <repository-url>
cd NVIDIA_DLSS
```

2. 安装依赖：
```bash
pip install -r requirements.txt
```

3. 下载NVIDIA DLSS SDK（需要NVIDIA开发者账号）

## 使用方法

### 基本用法
```bash
python dlss_video_interpolation.py --input video.mp4 --output output.mp4 --multiplier 2
```

### 参数说明
- `--input`: 输入视频文件路径
- `--output`: 输出视频文件路径
- `--multiplier`: 帧率倍数（2=60fps, 3=90fps, 4=120fps）
- `--quality`: 质量设置（performance, balanced, quality）
- `--gpu`: 指定GPU设备ID

### 示例
```bash
# 将30fps视频转换为60fps
python dlss_video_interpolation.py --input input_30fps.mp4 --output output_60fps.mp4 --multiplier 2

# 高质量模式，转换为120fps
python dlss_video_interpolation.py --input input_30fps.mp4 --output output_120fps.mp4 --multiplier 4 --quality quality
```

## 注意事项

- 确保NVIDIA GPU驱动是最新版本
- 处理大文件时请确保有足够的磁盘空间
- 建议使用SSD以提高I/O性能

## 许可证

MIT License 