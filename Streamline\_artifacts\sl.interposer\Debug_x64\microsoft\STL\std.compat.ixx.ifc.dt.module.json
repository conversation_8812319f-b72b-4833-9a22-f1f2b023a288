{"version": 1, "revision": 0, "rules": [{"primary-output": "..\\..\\_artifacts\\sl.interposer\\Debug_x64\\microsoft\\STL\\std.compat.obj", "outputs": ["..\\..\\_artifacts\\sl.interposer\\Debug_x64\\microsoft\\STL\\std.compat.ixx.ifc.dt.module.json", "..\\..\\_artifacts\\sl.interposer\\Debug_x64\\microsoft\\STL\\std.compat.ixx.ifc.dt.d.json", "..\\..\\_artifacts\\sl.interposer\\Debug_x64\\microsoft\\STL\\std.compat.ifc"], "provides": [{"logical-name": "std.compat", "source-path": "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\modules\\std.compat.ixx", "is-interface": true}], "requires": [{"logical-name": "std"}]}]}