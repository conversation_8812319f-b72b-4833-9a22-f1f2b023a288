# DLSS视频补帧 - 快速开始指南

## 🚀 快速开始

### 1. 环境要求
- **操作系统**: Windows 10/11
- **Python**: 3.8 或更高版本
- **GPU**: NVIDIA GPU (支持DLSS)
- **驱动**: 最新NVIDIA驱动

### 2. 一键安装
```bash
# 运行安装脚本
python install.py
```

### 3. 基本使用

#### 方法一：命令行
```bash
# 将30fps视频转换为60fps
python dlss_video_interpolation.py --input video.mp4 --output output_60fps.mp4 --multiplier 2

# 高质量模式，转换为120fps
python dlss_video_interpolation.py --input video.mp4 --output output_120fps.mp4 --multiplier 4 --quality quality
```

#### 方法二：批处理脚本 (Windows)
```bash
# 双击运行或命令行执行
run_dlss.bat input.mp4 output.mp4 2 quality
```

#### 方法三：Python代码
```python
from dlss_video_interpolation import VideoProcessor

# 创建处理器
processor = VideoProcessor("input.mp4", "output.mp4", multiplier=2)

# 处理视频
processor.process_video(quality='balanced', gpu_id=0)
```

## 📋 参数说明

| 参数 | 说明 | 默认值 | 选项 |
|------|------|--------|------|
| `--input` | 输入视频文件 | 必需 | 任何视频格式 |
| `--output` | 输出视频文件 | 必需 | MP4格式 |
| `--multiplier` | 帧率倍数 | 2 | 2, 3, 4 |
| `--quality` | 质量设置 | balanced | performance, balanced, quality |
| `--gpu` | GPU设备ID | 0 | 0, 1, 2... |

## 🎯 使用示例

### 示例1：30fps → 60fps
```bash
python dlss_video_interpolation.py --input movie_30fps.mp4 --output movie_60fps.mp4 --multiplier 2
```

### 示例2：高质量120fps
```bash
python dlss_video_interpolation.py --input game_30fps.mp4 --output game_120fps.mp4 --multiplier 4 --quality quality
```

### 示例3：批量处理
```bash
# 将input_videos目录下的所有视频转换为60fps
python example_usage.py --example batch
```

## ⚙️ 质量设置对比

| 质量模式 | 处理速度 | 输出质量 | 适用场景 |
|----------|----------|----------|----------|
| `performance` | 最快 | 一般 | 快速预览 |
| `balanced` | 中等 | 良好 | 日常使用 |
| `quality` | 较慢 | 最佳 | 高质量输出 |

## 🔧 故障排除

### 常见问题

**Q: 提示"CUDA不可用"**
- 确保安装了NVIDIA驱动
- 检查PyTorch是否支持CUDA
- 运行 `python test_dlss.py` 检查环境

**Q: 处理速度很慢**
- 使用 `performance` 质量模式
- 降低输入视频分辨率
- 确保GPU内存充足

**Q: 输出视频质量不佳**
- 使用 `quality` 质量模式
- 确保输入视频质量良好
- 检查GPU是否支持DLSS

**Q: 内存不足错误**
- 降低批处理大小
- 关闭其他占用GPU的程序
- 使用CPU模式（较慢）

### 性能优化建议

1. **GPU设置**
   - 使用支持DLSS的RTX系列显卡
   - 确保GPU驱动是最新版本
   - 关闭其他GPU密集型程序

2. **视频设置**
   - 输入视频分辨率建议不超过4K
   - 使用H.264或H.265编码
   - 避免使用过长的视频文件

3. **系统设置**
   - 使用SSD存储提高I/O性能
   - 确保有足够的系统内存
   - 关闭不必要的后台程序

## 📁 项目结构

```
NVIDIA_DLSS/
├── dlss_video_interpolation.py  # 主程序
├── dlss_integration.py          # DLSS集成
├── config.py                    # 配置文件
├── example_usage.py             # 使用示例
├── install.py                   # 安装脚本
├── test_dlss.py                 # 测试脚本
├── requirements.txt             # 依赖列表
├── README.md                    # 详细文档
├── QUICKSTART.md               # 快速开始
├── input_videos/               # 输入视频目录
├── output_videos/              # 输出视频目录
└── temp/                       # 临时文件目录
```

## 🆘 获取帮助

1. **运行测试**: `python test_dlss.py`
2. **查看示例**: `python example_usage.py --help`
3. **检查系统**: `python dlss_video_interpolation.py --check`

## 📞 技术支持

如果遇到问题，请：
1. 查看错误日志
2. 运行测试脚本
3. 检查系统要求
4. 参考详细文档

---

**注意**: 此项目需要NVIDIA DLSS SDK，请确保已正确安装相关组件。 