﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Develop|x64">
      <Configuration>Develop</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Production|x64">
      <Configuration>Production</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{C8238F97-3403-0E33-3D1B-9909A9797494}</ProjectGuid>
    <IgnoreWarnCompileDuplicatedFilename>true</IgnoreWarnCompileDuplicatedFilename>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>sl.dlss</RootNamespace>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Develop|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Production|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Develop|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Production|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>..\..\_artifacts\sl.dlss\Debug_x64\</OutDir>
    <IntDir>..\..\_artifacts\sl.dlss\Debug_x64\</IntDir>
    <TargetName>sl.dlss</TargetName>
    <TargetExt>.dll</TargetExt>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Develop|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>..\..\_artifacts\sl.dlss\Develop_x64\</OutDir>
    <IntDir>..\..\_artifacts\sl.dlss\Develop_x64\</IntDir>
    <TargetName>sl.dlss</TargetName>
    <TargetExt>.dll</TargetExt>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Production|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>..\..\_artifacts\sl.dlss\Production_x64\</OutDir>
    <IntDir>..\..\_artifacts\sl.dlss\Production_x64\</IntDir>
    <TargetName>sl.dlss</TargetName>
    <TargetExt>.dll</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <PreprocessorDefinitions>SL_SDK;SL_WINDOWS;WIN32;WIN64;_CONSOLE;NOMINMAX;DEBUG;SL_ENABLE_TIMING=1;SL_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\..\_artifacts;..\..;..\..\external\json\include;..\..\external\perf-sdk\include;..\..\external\perf-sdk\include\windows-desktop-x64;..\..\external\perf-sdk\NvPerfUtility\include;..\..\external\vulkan\Include;..\..\external\nvapi;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <Optimization>Disabled</Optimization>
      <MinimalRebuild>false</MinimalRebuild>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>SL_SDK;SL_WINDOWS;WIN32;WIN64;_CONSOLE;NOMINMAX;DEBUG;SL_ENABLE_TIMING=1;SL_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\..;..\..\external\json\include;..\..\external\perf-sdk\include;..\..\external\perf-sdk\include\windows-desktop-x64;..\..\external\perf-sdk\NvPerfUtility\include;..\..\external\vulkan\Include;..\..\external\nvapi;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>DebugFull</GenerateDebugInformation>
      <ImportLibrary>..\..\_artifacts\sl.dlss\Debug_x64\sl.dlss.lib</ImportLibrary>
      <TreatLinkerWarningAsErrors>true</TreatLinkerWarningAsErrors>
      <ProgramDatabaseFile>../../_artifacts/sl.dlss/Debug_x64/sl.dlss.pdb</ProgramDatabaseFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Develop|x64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <PreprocessorDefinitions>SL_SDK;SL_WINDOWS;WIN32;WIN64;_CONSOLE;NOMINMAX;NDEBUG;SL_ENABLE_TIMING=0;SL_ENABLE_PROFILING=0;SL_DEVELOP;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\..;..\..\external\json\include;..\..\external\perf-sdk\include;..\..\external\perf-sdk\include\windows-desktop-x64;..\..\external\perf-sdk\NvPerfUtility\include;..\..\external\vulkan\Include;..\..\external\nvapi;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <Optimization>Full</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MinimalRebuild>false</MinimalRebuild>
      <StringPooling>true</StringPooling>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>SL_SDK;SL_WINDOWS;WIN32;WIN64;_CONSOLE;NOMINMAX;NDEBUG;SL_ENABLE_TIMING=0;SL_ENABLE_PROFILING=0;SL_DEVELOP;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\..;..\..\external\json\include;..\..\external\perf-sdk\include;..\..\external\perf-sdk\include\windows-desktop-x64;..\..\external\perf-sdk\NvPerfUtility\include;..\..\external\vulkan\Include;..\..\external\nvapi;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <ImportLibrary>..\..\_artifacts\sl.dlss\Develop_x64\sl.dlss.lib</ImportLibrary>
      <TreatLinkerWarningAsErrors>true</TreatLinkerWarningAsErrors>
      <ProgramDatabaseFile>../../_artifacts/sl.dlss/Develop_x64/sl.dlss.pdb</ProgramDatabaseFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Production|x64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <PreprocessorDefinitions>SL_SDK;SL_WINDOWS;WIN32;WIN64;_CONSOLE;NOMINMAX;NDEBUG;SL_ENABLE_TIMING=0;SL_ENABLE_PROFILING=0;SL_PRODUCTION;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\..;..\..\external\json\include;..\..\external\perf-sdk\include;..\..\external\perf-sdk\include\windows-desktop-x64;..\..\external\perf-sdk\NvPerfUtility\include;..\..\external\vulkan\Include;..\..\external\nvapi;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <Optimization>Full</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MinimalRebuild>false</MinimalRebuild>
      <StringPooling>true</StringPooling>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdcpplatest</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>SL_SDK;SL_WINDOWS;WIN32;WIN64;_CONSOLE;NOMINMAX;NDEBUG;SL_ENABLE_TIMING=0;SL_ENABLE_PROFILING=0;SL_PRODUCTION;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\..;..\..\external\json\include;..\..\external\perf-sdk\include;..\..\external\perf-sdk\include\windows-desktop-x64;..\..\external\perf-sdk\NvPerfUtility\include;..\..\external\vulkan\Include;..\..\external\nvapi;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <ImportLibrary>..\..\_artifacts\sl.dlss\Production_x64\sl.dlss.lib</ImportLibrary>
      <TreatLinkerWarningAsErrors>true</TreatLinkerWarningAsErrors>
      <ProgramDatabaseFile>../../_artifacts/sl.dlss/Production_x64/sl.dlss.pdb</ProgramDatabaseFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\..\source\core\sl.api\internal.h" />
    <ClInclude Include="..\..\source\core\sl.api\internalDataSharing.h" />
    <ClInclude Include="..\..\source\core\sl.extra\extra.h" />
    <ClInclude Include="..\..\source\core\sl.file\file.h" />
    <ClInclude Include="..\..\source\core\sl.log\log.h" />
    <ClInclude Include="..\..\source\core\sl.plugin\plugin.h" />
    <ClInclude Include="..\..\source\core\sl.security\secureLoadLibrary.h" />
    <ClInclude Include="..\..\source\plugins\sl.dlss\dlss_shared.h" />
    <ClInclude Include="..\..\source\plugins\sl.dlss\resource.h" />
    <ClInclude Include="..\..\source\plugins\sl.dlss\versions.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\source\core\sl.plugin\plugin.cpp" />
    <ClCompile Include="..\..\source\core\sl.security\secureLoadLibrary.cpp" />
    <ClCompile Include="..\..\source\plugins\sl.dlss\dlssEntry.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\..\source\plugins\sl.dlss\plugin.rc" />
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="..\..\source\plugins\sl.dlss\dlss.json">
      <FileType>Document</FileType>
      <Command>copy "%(Identity)" "D:/Company/NVIDIA_DLSS/Streamline/_artifacts/json/%(Filename)%(Extension)"
pushd D:\Company\NVIDIA_DLSS\Streamline\_artifacts\json\
powershell.exe -NoProfile -ExecutionPolicy Bypass D:\Company\NVIDIA_DLSS\Streamline\tools\bin2cheader.ps1 -i "%(Filename).json" &gt; "D:/Company/NVIDIA_DLSS/Streamline/_artifacts/json/%(Filename)_json.h"
popd</Command>
      <Outputs>../../_artifacts/json/dlss.json</Outputs>
      <Message>Compiling %(Identity) to %(Filename)_json.h</Message>
    </CustomBuild>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>