"""
DLSS视频补帧配置文件
管理各种设置和参数
"""

import os
from typing import Dict, Any, Optional
import json

class DLSSConfig:
    """DLSS配置类"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置
        
        Args:
            config_file: 配置文件路径，如果为None则使用默认配置
        """
        self.config_file = config_file or "dlss_config.json"
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        default_config = {
            "dlss": {
                "library_path": None,  # DLSS库路径，None表示自动查找
                "quality_modes": {
                    "performance": {
                        "scale": 0.5,
                        "iterations": 1,
                        "description": "性能优先，速度最快"
                    },
                    "balanced": {
                        "scale": 0.75,
                        "iterations": 2,
                        "description": "平衡模式，速度和质量兼顾"
                    },
                    "quality": {
                        "scale": 1.0,
                        "iterations": 3,
                        "description": "质量优先，效果最好"
                    },
                    "ultra_quality": {
                        "scale": 1.0,
                        "iterations": 4,
                        "description": "超高质量，最高效果"
                    }
                },
                "supported_resolutions": [
                    [1920, 1080],
                    [2560, 1440],
                    [3840, 2160],
                    [1280, 720]
                ]
            },
            "video": {
                "supported_formats": [".mp4", ".avi", ".mov", ".mkv", ".wmv"],
                "codec": "mp4v",
                "default_fps": 30,
                "max_fps": 120,
                "quality_presets": {
                    "fast": {
                        "crf": 23,
                        "preset": "fast"
                    },
                    "medium": {
                        "crf": 20,
                        "preset": "medium"
                    },
                    "slow": {
                        "crf": 18,
                        "preset": "slow"
                    }
                }
            },
            "processing": {
                "batch_size": 1,
                "max_memory_gb": 8,
                "temp_directory": "temp",
                "log_level": "INFO",
                "gpu_memory_fraction": 0.8
            },
            "advanced": {
                "enable_motion_vectors": True,
                "enable_depth_buffer": False,
                "enable_sharpening": True,
                "sharpness_strength": 0.5,
                "enable_temporal_stability": True
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                # 合并用户配置和默认配置
                self._merge_configs(default_config, user_config)
            except Exception as e:
                print(f"加载配置文件失败: {e}，使用默认配置")
        
        return default_config
    
    def _merge_configs(self, default: Dict[str, Any], user: Dict[str, Any]):
        """合并配置"""
        for key, value in user.items():
            if key in default:
                if isinstance(value, dict) and isinstance(default[key], dict):
                    self._merge_configs(default[key], value)
                else:
                    default[key] = value
            else:
                default[key] = value
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            print(f"配置已保存到: {self.config_file}")
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def get_dlss_quality_settings(self, quality: str) -> Dict[str, Any]:
        """获取DLSS质量设置"""
        return self.config["dlss"]["quality_modes"].get(quality, {})
    
    def get_video_settings(self) -> Dict[str, Any]:
        """获取视频设置"""
        return self.config["video"]
    
    def get_processing_settings(self) -> Dict[str, Any]:
        """获取处理设置"""
        return self.config["processing"]
    
    def get_advanced_settings(self) -> Dict[str, Any]:
        """获取高级设置"""
        return self.config["advanced"]
    
    def update_dlss_library_path(self, path: str):
        """更新DLSS库路径"""
        self.config["dlss"]["library_path"] = path
        self.save_config()
    
    def add_supported_resolution(self, width: int, height: int):
        """添加支持的分辨率"""
        resolution = [width, height]
        if resolution not in self.config["dlss"]["supported_resolutions"]:
            self.config["dlss"]["supported_resolutions"].append(resolution)
            self.save_config()
    
    def set_processing_batch_size(self, batch_size: int):
        """设置批处理大小"""
        self.config["processing"]["batch_size"] = batch_size
        self.save_config()
    
    def set_gpu_memory_fraction(self, fraction: float):
        """设置GPU内存使用比例"""
        self.config["processing"]["gpu_memory_fraction"] = max(0.1, min(1.0, fraction))
        self.save_config()

class VideoProcessingConfig:
    """视频处理配置"""
    
    def __init__(self, config: DLSSConfig):
        self.config = config
    
    def get_optimal_settings(self, input_resolution: tuple, target_fps: int, 
                           quality: str = 'balanced') -> Dict[str, Any]:
        """
        根据输入参数获取最优设置
        
        Args:
            input_resolution: 输入分辨率 (width, height)
            target_fps: 目标帧率
            quality: 质量设置
            
        Returns:
            最优处理设置
        """
        width, height = input_resolution
        
        # 获取DLSS质量设置
        dlss_settings = self.config.get_dlss_quality_settings(quality)
        
        # 计算输出分辨率
        scale = dlss_settings.get('scale', 0.75)
        output_width = int(width * scale)
        output_height = int(height * scale)
        
        # 获取视频设置
        video_settings = self.config.get_video_settings()
        
        # 获取处理设置
        processing_settings = self.config.get_processing_settings()
        
        # 获取高级设置
        advanced_settings = self.config.get_advanced_settings()
        
        return {
            "input_resolution": (width, height),
            "output_resolution": (output_width, output_height),
            "input_fps": video_settings["default_fps"],
            "target_fps": min(target_fps, video_settings["max_fps"]),
            "quality": quality,
            "dlss_settings": dlss_settings,
            "video_settings": video_settings,
            "processing_settings": processing_settings,
            "advanced_settings": advanced_settings,
            "batch_size": processing_settings["batch_size"],
            "gpu_memory_fraction": processing_settings["gpu_memory_fraction"]
        }
    
    def validate_settings(self, settings: Dict[str, Any]) -> bool:
        """验证设置是否有效"""
        try:
            # 检查分辨率
            input_res = settings["input_resolution"]
            output_res = settings["output_resolution"]
            
            if input_res[0] <= 0 or input_res[1] <= 0:
                return False
            
            if output_res[0] <= 0 or output_res[1] <= 0:
                return False
            
            # 检查帧率
            if settings["target_fps"] <= 0:
                return False
            
            # 检查质量设置
            if settings["quality"] not in self.config.config["dlss"]["quality_modes"]:
                return False
            
            return True
            
        except KeyError:
            return False

# 全局配置实例
config = DLSSConfig()
video_config = VideoProcessingConfig(config)

def get_config() -> DLSSConfig:
    """获取全局配置实例"""
    return config

def get_video_config() -> VideoProcessingConfig:
    """获取视频处理配置实例"""
    return video_config 