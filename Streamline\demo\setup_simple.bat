@echo off
echo DLSS Demo Environment Setup
echo ===========================

if not exist "simple_dlss_demo.cpp" (
    echo Error: Demo files not found!
    pause
    exit /b 1
)

echo Creating directories...
if not exist "..\bin" mkdir "..\bin"
if not exist "..\bin\x64" mkdir "..\bin\x64"
if not exist "..\lib" mkdir "..\lib"
if not exist "..\lib\x64" mkdir "..\lib\x64"

echo Checking for Streamline artifacts...
set FOUND=0

if exist "..\_artifacts\sl.interposer\Debug_x64\sl.interposer.dll" (
    echo Found sl.interposer.dll
    copy "..\_artifacts\sl.interposer\Debug_x64\sl.interposer.dll" "..\bin\x64\" >nul
    set /a FOUND+=1
)

if exist "..\_artifacts\sl.common\Debug_x64\sl.common.dll" (
    echo Found sl.common.dll
    copy "..\_artifacts\sl.common\Debug_x64\sl.common.dll" "..\bin\x64\" >nul
    set /a FOUND+=1
)

if exist "..\_artifacts\sl.dlss\Debug_x64\sl.dlss.dll" (
    echo Found sl.dlss.dll
    copy "..\_artifacts\sl.dlss\Debug_x64\sl.dlss.dll" "..\bin\x64\" >nul
    set /a FOUND+=1
)

echo Copying config files...
if exist "..\scripts\sl.interposer.json" (
    copy "..\scripts\sl.interposer.json" "..\bin\x64\" >nul
)
if exist "..\scripts\sl.common.json" (
    copy "..\scripts\sl.common.json" "..\bin\x64\" >nul
)

echo.
echo Found %FOUND% Streamline DLLs
echo.

if %FOUND% GEQ 3 (
    echo Ready to build demos!
    echo Run: build_demo.bat
) else (
    echo Missing DLLs. Need to build Streamline first.
    echo Run: cd .. && build.bat -debug
)

echo.
echo Note: You also need nvngx_dlss.dll from DLSS SDK
echo.
pause
