cmake_minimum_required(VERSION 3.16)
project(SimpleDLSSDemo)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Platform check
if(NOT WIN32)
    message(FATAL_ERROR "This demo currently only supports Windows")
endif()

# Find required packages
find_package(PkgConfig QUIET)

# Set up include directories
set(STREAMLINE_ROOT "${CMAKE_CURRENT_SOURCE_DIR}/..")
set(STREAMLINE_INCLUDE_DIR "${STREAMLINE_ROOT}/include")

# Check if Streamline headers exist
if(NOT EXISTS "${STREAMLINE_INCLUDE_DIR}/sl.h")
    message(FATAL_ERROR "Streamline headers not found at ${STREAMLINE_INCLUDE_DIR}")
endif()

# Create the simple demo executable
add_executable(SimpleDLSSDemo
    simple_dlss_demo.cpp
)

# Create the advanced demo executable
add_executable(AdvancedDLSSDemo
    advanced_dlss_demo.cpp
)

# Create the DLSS-D demo executable
add_executable(DLSSDDemo
    dlss_d_demo.cpp
)

# Create the simple DLSS-D demo executable
add_executable(SimpleDLSSDDemo
    simple_dlss_d_demo.cpp
)

# Set target properties for both demos
target_include_directories(SimpleDLSSDemo PRIVATE
    ${STREAMLINE_INCLUDE_DIR}
)

target_include_directories(AdvancedDLSSDemo PRIVATE
    ${STREAMLINE_INCLUDE_DIR}
)

target_include_directories(DLSSDDemo PRIVATE
    ${STREAMLINE_INCLUDE_DIR}
)

target_include_directories(SimpleDLSSDDemo PRIVATE
    ${STREAMLINE_INCLUDE_DIR}
)

# Link libraries for both demos
target_link_libraries(SimpleDLSSDemo PRIVATE
    d3d12
    dxgi
    dxguid
)

target_link_libraries(AdvancedDLSSDemo PRIVATE
    d3d12
    dxgi
    dxguid
)

target_link_libraries(DLSSDDemo PRIVATE
    d3d12
    dxgi
    dxguid
)

target_link_libraries(SimpleDLSSDDemo PRIVATE
    # Link Streamline library
    "${CMAKE_CURRENT_SOURCE_DIR}/../lib/x64/sl.interposer.lib"
)

# Compiler-specific options
if(MSVC)
    target_compile_options(SimpleDLSSDemo PRIVATE
        /W4
        /WX-  # Don't treat warnings as errors for demo
    )

    target_compile_options(AdvancedDLSSDemo PRIVATE
        /W4
        /WX-  # Don't treat warnings as errors for demo
    )

    target_compile_options(DLSSDDemo PRIVATE
        /W4
        /WX-  # Don't treat warnings as errors for demo
    )

    target_compile_options(SimpleDLSSDDemo PRIVATE
        /W4
        /WX-  # Don't treat warnings as errors for demo
    )

    # Set subsystem to console for simple demo
    set_target_properties(SimpleDLSSDemo PROPERTIES
        LINK_FLAGS "/SUBSYSTEM:CONSOLE"
    )

    # Set subsystem to windows for advanced demo
    set_target_properties(AdvancedDLSSDemo PROPERTIES
        LINK_FLAGS "/SUBSYSTEM:WINDOWS"
    )

    # Set subsystem to windows for DLSS-D demo
    set_target_properties(DLSSDDemo PROPERTIES
        LINK_FLAGS "/SUBSYSTEM:WINDOWS"
    )

    # Set subsystem to console for simple DLSS-D demo
    set_target_properties(SimpleDLSSDDemo PROPERTIES
        LINK_FLAGS "/SUBSYSTEM:CONSOLE"
    )
endif()

# Set output directory
set_target_properties(SimpleDLSSDemo PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_CURRENT_BINARY_DIR}/bin"
)

set_target_properties(AdvancedDLSSDemo PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_CURRENT_BINARY_DIR}/bin"
)

set_target_properties(DLSSDDemo PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_CURRENT_BINARY_DIR}/bin"
)

set_target_properties(SimpleDLSSDDemo PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_CURRENT_BINARY_DIR}/bin"
)

# Copy Streamline DLLs if they exist
set(STREAMLINE_BIN_DIR "${STREAMLINE_ROOT}/bin/x64")
set(STREAMLINE_LIB_DIR "${STREAMLINE_ROOT}/lib/x64")

# List of required Streamline DLLs
set(STREAMLINE_DLLS
    "sl.interposer.dll"
    "sl.common.dll"
    "sl.dlss.dll"
    "sl.dlss_d.dll"
)

# List of required DLSS DLLs
set(DLSS_DLLS
    "nvngx_dlss.dll"
)

# Function to copy DLLs if they exist
function(copy_dll_if_exists dll_name source_dir target_dir)
    set(dll_path "${source_dir}/${dll_name}")
    if(EXISTS "${dll_path}")
        message(STATUS "Found ${dll_name} at ${dll_path}")
        add_custom_command(TARGET SimpleDLSSDemo POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy_if_different
            "${dll_path}"
            "${target_dir}/${dll_name}"
            COMMENT "Copying ${dll_name}"
        )
    else()
        message(WARNING "${dll_name} not found at ${dll_path}")
    endif()
endfunction()

# Copy Streamline DLLs
foreach(dll ${STREAMLINE_DLLS})
    copy_dll_if_exists("${dll}" "${STREAMLINE_BIN_DIR}" "$<TARGET_FILE_DIR:SimpleDLSSDemo>")
    copy_dll_if_exists("${dll}" "${STREAMLINE_BIN_DIR}/development" "$<TARGET_FILE_DIR:SimpleDLSSDemo>")
endforeach()

# Copy DLSS DLLs
foreach(dll ${DLSS_DLLS})
    copy_dll_if_exists("${dll}" "${STREAMLINE_BIN_DIR}" "$<TARGET_FILE_DIR:SimpleDLSSDemo>")
    copy_dll_if_exists("${dll}" "${STREAMLINE_BIN_DIR}/development" "$<TARGET_FILE_DIR:SimpleDLSSDemo>")
endforeach()

# Copy JSON configuration files
set(STREAMLINE_SCRIPTS_DIR "${STREAMLINE_ROOT}/scripts")
set(JSON_FILES
    "sl.interposer.json"
    "sl.common.json"
)

foreach(json_file ${JSON_FILES})
    set(json_path "${STREAMLINE_SCRIPTS_DIR}/${json_file}")
    if(EXISTS "${json_path}")
        message(STATUS "Found ${json_file} at ${json_path}")
        add_custom_command(TARGET SimpleDLSSDemo POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy_if_different
            "${json_path}"
            "$<TARGET_FILE_DIR:SimpleDLSSDemo>/${json_file}"
            COMMENT "Copying ${json_file}"
        )
    else()
        message(WARNING "${json_file} not found at ${json_path}")
    endif()
endforeach()

# Print build information
message(STATUS "=== SimpleDLSSDemo Build Configuration ===")
message(STATUS "Streamline Root: ${STREAMLINE_ROOT}")
message(STATUS "Streamline Include: ${STREAMLINE_INCLUDE_DIR}")
message(STATUS "Streamline Bin: ${STREAMLINE_BIN_DIR}")
message(STATUS "Target Architecture: x64")
message(STATUS "==========================================")

# Add a custom target to show usage instructions
add_custom_target(show_usage
    COMMAND ${CMAKE_COMMAND} -E echo ""
    COMMAND ${CMAKE_COMMAND} -E echo "=== SimpleDLSSDemo Usage ==="
    COMMAND ${CMAKE_COMMAND} -E echo "1. Build the project: cmake --build ."
    COMMAND ${CMAKE_COMMAND} -E echo "2. Run the demo: ./bin/SimpleDLSSDemo.exe"
    COMMAND ${CMAKE_COMMAND} -E echo ""
    COMMAND ${CMAKE_COMMAND} -E echo "Note: This demo requires:"
    COMMAND ${CMAKE_COMMAND} -E echo "- NVIDIA RTX GPU with DLSS support"
    COMMAND ${CMAKE_COMMAND} -E echo "- Streamline DLLs in bin/x64 directory"
    COMMAND ${CMAKE_COMMAND} -E echo "- DLSS DLLs (nvngx_dlss.dll)"
    COMMAND ${CMAKE_COMMAND} -E echo "============================="
    VERBATIM
)
