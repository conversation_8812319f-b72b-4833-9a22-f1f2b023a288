{"Version": "1.2", "Data": {"Source": "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\modules\\std.ixx", "ProvidedModule": "std", "Includes": ["d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\assert.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\vcruntime.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\sal.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\concurrencysal.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\vadefs.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\ctype.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wctype.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\errno.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\fenv.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\float.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\inttypes.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\stdint.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\limits.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\locale.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\math.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_math.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\setjmp.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\signal.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\stdarg.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\stddef.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\stdio.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wstdio.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_stdio_config.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\stdlib.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_malloc.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_search.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wstdlib.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\string.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_memory.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_memcpy_s.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\vcruntime_string.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wstring.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\time.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wtime.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\uchar.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\wchar.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wconio.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wdirect.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wio.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_share.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wprocess.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\sys\\stat.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\sys\\types.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\wctype.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\intrin.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\intrin0.inl.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\immintrin.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\wmmintrin.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\nmmintrin.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\smmintrin.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\tmmintrin.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\pmmintrin.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\emmintrin.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xmmintrin.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\mmintrin.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\malloc.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\zmmintrin.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\ammintrin.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\algorithm", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\yvals_core.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xkeycheck.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\__msvc_heap_algorithms.hpp", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xutility", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\yvals.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\crtdbg.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\vcruntime_new_debug.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\vcruntime_new.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\crtdefs.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\use_ansi.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\__msvc_iter_core.hpp", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\utility", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\initializer_list", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\cstddef", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xtr1common", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\type_traits", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\cstdint", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\cstring", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\compare", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\concepts", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\cstdlib", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\math.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\climits", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\cwchar", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\cstdio", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\__msvc_minmax.hpp", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xmemory", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\limits", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\cfloat", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\intrin0.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\new", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\exception", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\vcruntime_exception.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\eh.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_terminate.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xatomic.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\tuple", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\optional", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xsmf_control.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\any", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\typeinfo", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\vcruntime_typeinfo.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\array", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\atomic", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xatomic_wait.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xthreads.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\__msvc_threads_core.hpp", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xtimec.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\ctime", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\barrier", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\bit", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\__msvc_bit_utils.hpp", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\bitset", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\iosfwd", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xstring", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\__msvc_sanitizer_annotate_container.hpp", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\__msvc_string_view.hpp", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xpolymorphic_allocator.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\charconv", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xbit_ops.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xcharconv.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xerrc.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xcharconv_ryu.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xcharconv_ryu_tables.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xcharconv_tables.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\chrono", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\__msvc_chrono.hpp", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\ratio", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\system_error", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\__msvc_system_error_abi.hpp", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\cerrno", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\stdexcept", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xcall_once.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xfilesystem_abi.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\__msvc_tzdb.hpp", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\cmath", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\format", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\__msvc_format_ucd_tables.hpp", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\__msvc_formatter.hpp", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\__msvc_print.hpp", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\__msvc_ranges_tuple_formatter.hpp", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\iterator", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xlocale", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\memory", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xfacet", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xlocinfo", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\__msvc_xlocinfo_types.hpp", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\cctype", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\clocale", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\locale", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xlocbuf", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\streambuf", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xiosbase", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\share.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xlocmes", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xlocmon", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xlocnum", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xloctime", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\forward_list", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\iomanip", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\istream", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\__msvc_ostream.hpp", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\ios", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\sstream", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\string", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\vector", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\codecvt", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\complex", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\ymath.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\condition_variable", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\mutex", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\thread", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\process.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_startup.h", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\math.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\vcruntime_startup.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\stop_token", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\coroutine", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\deque", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\execution", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\numeric", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\queue", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\__msvc_ranges_to.hpp", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\expected", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\filesystem", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\fstream", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\__msvc_filebuf.hpp", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\functional", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\unordered_map", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xhash", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\list", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xnode_handle.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\future", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\ppltasks.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\pplwin.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\pplinterface.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\ppltaskscheduler.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\crtdefs.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\pplcancellation_token.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\generator", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\iostream", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\ostream", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\latch", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\map", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\xtree", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\mdspan", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\span", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\memory_resource", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\numbers", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\print", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\random", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\__msvc_int128.hpp", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\ranges", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\string_view", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\regex", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\scoped_allocator", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\semaphore", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\set", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\shared_mutex", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\source_location", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\spanstream", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\stack", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\stacktrace", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\stdfloat", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\strstream", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\syncstream", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\typeindex", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\unordered_set", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\valarray", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\variant", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\version", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\cassert", "d:\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\assert.h", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\cfenv", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\cinttypes", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\csetjmp", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\csignal", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\cstdarg", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\cuchar", "d:\\vs2022\\community\\vc\\tools\\msvc\\14.43.34808\\include\\cwctype"], "ImportedModules": [], "ImportedHeaderUnits": []}}