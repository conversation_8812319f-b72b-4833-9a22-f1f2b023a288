/*
* NVIDIA DLSS-D (DLSS Denoiser) Demo Application
* 
* This demo shows how to use DLSS-D for denoising ray-traced images.
* DLSS-D is specifically designed for denoising ray-traced reflections,
* global illumination, and other ray-traced effects.
*/

#include <iostream>
#include <windows.h>
#include <d3d12.h>
#include <dxgi1_6.h>
#include <wrl/client.h>
#include <vector>
#include <memory>

// Streamline headers
#include "../include/sl.h"
#include "../include/sl_dlss_d.h"
#include "../include/sl_consts.h"

using Microsoft::WRL::ComPtr;

class DLSSDDemo {
private:
    // D3D12 Core objects
    ComPtr<ID3D12Device> m_device;
    ComPtr<IDXGISwapChain3> m_swapChain;
    ComPtr<ID3D12CommandQueue> m_commandQueue;
    ComPtr<ID3D12CommandAllocator> m_commandAllocator;
    ComPtr<ID3D12GraphicsCommandList> m_commandList;
    ComPtr<ID3D12Fence> m_fence;
    HANDLE m_fenceEvent;
    UINT64 m_fenceValue;
    
    // Render targets
    static const UINT FrameCount = 2;
    ComPtr<ID3D12Resource> m_renderTargets[FrameCount];
    ComPtr<ID3D12DescriptorHeap> m_rtvHeap;
    UINT m_rtvDescriptorSize;
    UINT m_frameIndex;
    
    // DLSS-D resources
    ComPtr<ID3D12Resource> m_noisyColorBuffer;      // Input: noisy ray-traced image
    ComPtr<ID3D12Resource> m_albedoBuffer;          // Input: surface albedo
    ComPtr<ID3D12Resource> m_normalBuffer;          // Input: world space normals
    ComPtr<ID3D12Resource> m_depthBuffer;           // Input: depth buffer
    ComPtr<ID3D12Resource> m_motionVectorBuffer;    // Input: motion vectors
    ComPtr<ID3D12Resource> m_denoisedOutput;        // Output: denoised image
    
    // Demo settings
    UINT m_width = 1920;
    UINT m_height = 1080;
    
    bool m_dlssdInitialized = false;
    sl::DLSSDOptions m_dlssdOptions = {};
    
    HWND m_hwnd = nullptr;
    
public:
    DLSSDDemo() = default;
    ~DLSSDDemo() { Cleanup(); }
    
    bool Initialize(HWND hwnd);
    bool InitializeDLSSD();
    void Update();
    void Render();
    void Present();
    void Cleanup();
    
private:
    bool CreateD3D12Device();
    bool CreateSwapChain();
    bool CreateRenderTargets();
    bool CreateDLSSDResources();
    bool InitializeStreamline();
    void WaitForPreviousFrame();
    void PopulateCommandList();
    void RenderNoisyScene();
    void EvaluateDLSSD();
    void SimulateNoisyRayTracing();
    
    static void LogCallback(sl::LogLevel level, const char* message);
};

void DLSSDDemo::LogCallback(sl::LogLevel level, const char* message) {
    const char* levelStr = "UNKNOWN";
    switch (level) {
        case sl::eLogLevelOff: return;
        case sl::eLogLevelDefault: levelStr = "DEFAULT"; break;
        case sl::eLogLevelVerbose: levelStr = "VERBOSE"; break;
        case sl::eLogLevelInfo: levelStr = "INFO"; break;
        case sl::eLogLevelWarn: levelStr = "WARN"; break;
        case sl::eLogLevelError: levelStr = "ERROR"; break;
    }
    std::cout << "[SL " << levelStr << "] " << message << std::endl;
}

bool DLSSDDemo::Initialize(HWND hwnd) {
    m_hwnd = hwnd;
    
    std::cout << "=== DLSS-D Denoiser Demo ===" << std::endl;
    std::cout << "Resolution: " << m_width << "x" << m_height << std::endl;
    std::cout << "DLSS-D is designed for denoising ray-traced effects" << std::endl;
    std::cout << "=====================================" << std::endl;
    
    if (!CreateD3D12Device()) {
        std::cerr << "Failed to create D3D12 device" << std::endl;
        return false;
    }
    
    if (!CreateSwapChain()) {
        std::cerr << "Failed to create swap chain" << std::endl;
        return false;
    }
    
    if (!CreateRenderTargets()) {
        std::cerr << "Failed to create render targets" << std::endl;
        return false;
    }
    
    if (!InitializeStreamline()) {
        std::cerr << "Failed to initialize Streamline" << std::endl;
        return false;
    }
    
    if (!CreateDLSSDResources()) {
        std::cerr << "Failed to create DLSS-D resources" << std::endl;
        return false;
    }
    
    if (!InitializeDLSSD()) {
        std::cerr << "Failed to initialize DLSS-D" << std::endl;
        return false;
    }
    
    std::cout << "DLSS-D Demo initialized successfully!" << std::endl;
    return true;
}

bool DLSSDDemo::CreateD3D12Device() {
    std::cout << "Creating D3D12 device..." << std::endl;
    
#ifdef _DEBUG
    ComPtr<ID3D12Debug> debugController;
    if (SUCCEEDED(D3D12GetDebugInterface(IID_PPV_ARGS(&debugController)))) {
        debugController->EnableDebugLayer();
    }
#endif
    
    ComPtr<IDXGIFactory4> factory;
    HRESULT hr = CreateDXGIFactory1(IID_PPV_ARGS(&factory));
    if (FAILED(hr)) return false;
    
    ComPtr<IDXGIAdapter1> adapter;
    for (UINT adapterIndex = 0; DXGI_ERROR_NOT_FOUND != factory->EnumAdapters1(adapterIndex, &adapter); ++adapterIndex) {
        DXGI_ADAPTER_DESC1 desc;
        adapter->GetDesc1(&desc);
        
        if (desc.Flags & DXGI_ADAPTER_FLAG_SOFTWARE) continue;
        
        hr = D3D12CreateDevice(adapter.Get(), D3D_FEATURE_LEVEL_11_0, IID_PPV_ARGS(&m_device));
        if (SUCCEEDED(hr)) {
            std::wcout << L"Using adapter: " << desc.Description << std::endl;
            break;
        }
    }
    
    if (!m_device) return false;
    
    // Create command queue
    D3D12_COMMAND_QUEUE_DESC queueDesc = {};
    queueDesc.Flags = D3D12_COMMAND_QUEUE_FLAG_NONE;
    queueDesc.Type = D3D12_COMMAND_LIST_TYPE_DIRECT;
    
    hr = m_device->CreateCommandQueue(&queueDesc, IID_PPV_ARGS(&m_commandQueue));
    if (FAILED(hr)) return false;
    
    // Create command allocator
    hr = m_device->CreateCommandAllocator(D3D12_COMMAND_LIST_TYPE_DIRECT, IID_PPV_ARGS(&m_commandAllocator));
    if (FAILED(hr)) return false;
    
    // Create command list
    hr = m_device->CreateCommandList(0, D3D12_COMMAND_LIST_TYPE_DIRECT, m_commandAllocator.Get(), nullptr, IID_PPV_ARGS(&m_commandList));
    if (FAILED(hr)) return false;
    
    m_commandList->Close();
    
    // Create synchronization objects
    hr = m_device->CreateFence(0, D3D12_FENCE_FLAG_NONE, IID_PPV_ARGS(&m_fence));
    if (FAILED(hr)) return false;
    
    m_fenceValue = 1;
    m_fenceEvent = CreateEvent(nullptr, FALSE, FALSE, nullptr);
    if (m_fenceEvent == nullptr) return false;
    
    return true;
}

bool DLSSDDemo::CreateSwapChain() {
    std::cout << "Creating swap chain..." << std::endl;
    
    ComPtr<IDXGIFactory4> factory;
    HRESULT hr = CreateDXGIFactory1(IID_PPV_ARGS(&factory));
    if (FAILED(hr)) return false;
    
    DXGI_SWAP_CHAIN_DESC1 swapChainDesc = {};
    swapChainDesc.BufferCount = FrameCount;
    swapChainDesc.Width = m_width;
    swapChainDesc.Height = m_height;
    swapChainDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    swapChainDesc.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    swapChainDesc.SwapEffect = DXGI_SWAP_EFFECT_FLIP_DISCARD;
    swapChainDesc.SampleDesc.Count = 1;
    
    ComPtr<IDXGISwapChain1> swapChain;
    hr = factory->CreateSwapChainForHwnd(
        m_commandQueue.Get(),
        m_hwnd,
        &swapChainDesc,
        nullptr,
        nullptr,
        &swapChain
    );
    
    if (FAILED(hr)) return false;
    
    hr = swapChain.As(&m_swapChain);
    if (FAILED(hr)) return false;
    
    m_frameIndex = m_swapChain->GetCurrentBackBufferIndex();
    
    return true;
}

bool DLSSDDemo::CreateRenderTargets() {
    std::cout << "Creating render targets..." << std::endl;
    
    // Create descriptor heap for RTV
    D3D12_DESCRIPTOR_HEAP_DESC rtvHeapDesc = {};
    rtvHeapDesc.NumDescriptors = FrameCount;
    rtvHeapDesc.Type = D3D12_DESCRIPTOR_HEAP_TYPE_RTV;
    rtvHeapDesc.Flags = D3D12_DESCRIPTOR_HEAP_FLAG_NONE;
    
    HRESULT hr = m_device->CreateDescriptorHeap(&rtvHeapDesc, IID_PPV_ARGS(&m_rtvHeap));
    if (FAILED(hr)) return false;
    
    m_rtvDescriptorSize = m_device->GetDescriptorHandleIncrementSize(D3D12_DESCRIPTOR_HEAP_TYPE_RTV);
    
    // Create frame resources
    CD3DX12_CPU_DESCRIPTOR_HANDLE rtvHandle(m_rtvHeap->GetCPUDescriptorHandleForHeapStart());
    
    for (UINT n = 0; n < FrameCount; n++) {
        hr = m_swapChain->GetBuffer(n, IID_PPV_ARGS(&m_renderTargets[n]));
        if (FAILED(hr)) return false;
        
        m_device->CreateRenderTargetView(m_renderTargets[n].Get(), nullptr, rtvHandle);
        rtvHandle.Offset(1, m_rtvDescriptorSize);
    }
    
    return true;
}

bool DLSSDDemo::InitializeStreamline() {
    std::cout << "Initializing Streamline..." << std::endl;
    
    sl::Preferences pref = {};
    pref.showConsole = true;
    pref.logLevel = sl::eLogLevelInfo;
    pref.pathsToPlugins = nullptr;
    pref.numPathsToPlugins = 0;
    pref.pathToLogsAndData = nullptr;
    pref.logMessageCallback = LogCallback;
    pref.applicationId = 0; // Get from NVIDIA
    pref.engineType = sl::eEngineTypeCustom;
    pref.engineVersion = "1.0.0";
    pref.projectId = "DLSSDDemo";
    
    sl::Result result = sl::slInit(pref);
    if (result != sl::eOk) {
        std::cerr << "Failed to initialize Streamline: " << (int)result << std::endl;
        return false;
    }
    
    return true;
}

bool DLSSDDemo::CreateDLSSDResources() {
    std::cout << "Creating DLSS-D resources..." << std::endl;
    
    CD3DX12_HEAP_PROPERTIES heapProps(D3D12_HEAP_TYPE_DEFAULT);
    
    // Create noisy color buffer (input from ray tracing)
    D3D12_RESOURCE_DESC colorDesc = {};
    colorDesc.Dimension = D3D12_RESOURCE_DIMENSION_TEXTURE2D;
    colorDesc.Width = m_width;
    colorDesc.Height = m_height;
    colorDesc.DepthOrArraySize = 1;
    colorDesc.MipLevels = 1;
    colorDesc.Format = DXGI_FORMAT_R16G16B16A16_FLOAT; // HDR format for ray tracing
    colorDesc.SampleDesc.Count = 1;
    colorDesc.Flags = D3D12_RESOURCE_FLAG_ALLOW_RENDER_TARGET | D3D12_RESOURCE_FLAG_ALLOW_UNORDERED_ACCESS;
    
    HRESULT hr = m_device->CreateCommittedResource(
        &heapProps,
        D3D12_HEAP_FLAG_NONE,
        &colorDesc,
        D3D12_RESOURCE_STATE_RENDER_TARGET,
        nullptr,
        IID_PPV_ARGS(&m_noisyColorBuffer)
    );
    if (FAILED(hr)) return false;
    
    // Create albedo buffer
    hr = m_device->CreateCommittedResource(
        &heapProps,
        D3D12_HEAP_FLAG_NONE,
        &colorDesc,
        D3D12_RESOURCE_STATE_RENDER_TARGET,
        nullptr,
        IID_PPV_ARGS(&m_albedoBuffer)
    );
    if (FAILED(hr)) return false;
    
    // Create normal buffer
    D3D12_RESOURCE_DESC normalDesc = colorDesc;
    normalDesc.Format = DXGI_FORMAT_R16G16B16A16_SNORM; // Signed normalized for normals
    
    hr = m_device->CreateCommittedResource(
        &heapProps,
        D3D12_HEAP_FLAG_NONE,
        &normalDesc,
        D3D12_RESOURCE_STATE_RENDER_TARGET,
        nullptr,
        IID_PPV_ARGS(&m_normalBuffer)
    );
    if (FAILED(hr)) return false;
    
    // Create depth buffer
    D3D12_RESOURCE_DESC depthDesc = {};
    depthDesc.Dimension = D3D12_RESOURCE_DIMENSION_TEXTURE2D;
    depthDesc.Width = m_width;
    depthDesc.Height = m_height;
    depthDesc.DepthOrArraySize = 1;
    depthDesc.MipLevels = 1;
    depthDesc.Format = DXGI_FORMAT_R32_FLOAT; // Linear depth
    depthDesc.SampleDesc.Count = 1;
    depthDesc.Flags = D3D12_RESOURCE_FLAG_ALLOW_RENDER_TARGET;
    
    hr = m_device->CreateCommittedResource(
        &heapProps,
        D3D12_HEAP_FLAG_NONE,
        &depthDesc,
        D3D12_RESOURCE_STATE_RENDER_TARGET,
        nullptr,
        IID_PPV_ARGS(&m_depthBuffer)
    );
    if (FAILED(hr)) return false;
    
    // Create motion vector buffer
    D3D12_RESOURCE_DESC mvDesc = {};
    mvDesc.Dimension = D3D12_RESOURCE_DIMENSION_TEXTURE2D;
    mvDesc.Width = m_width;
    mvDesc.Height = m_height;
    mvDesc.DepthOrArraySize = 1;
    mvDesc.MipLevels = 1;
    mvDesc.Format = DXGI_FORMAT_R16G16_FLOAT;
    mvDesc.SampleDesc.Count = 1;
    mvDesc.Flags = D3D12_RESOURCE_FLAG_ALLOW_RENDER_TARGET;
    
    hr = m_device->CreateCommittedResource(
        &heapProps,
        D3D12_HEAP_FLAG_NONE,
        &mvDesc,
        D3D12_RESOURCE_STATE_RENDER_TARGET,
        nullptr,
        IID_PPV_ARGS(&m_motionVectorBuffer)
    );
    if (FAILED(hr)) return false;
    
    // Create denoised output buffer
    D3D12_RESOURCE_DESC outputDesc = colorDesc;
    outputDesc.Flags = D3D12_RESOURCE_FLAG_ALLOW_UNORDERED_ACCESS;
    
    hr = m_device->CreateCommittedResource(
        &heapProps,
        D3D12_HEAP_FLAG_NONE,
        &outputDesc,
        D3D12_RESOURCE_STATE_UNORDERED_ACCESS,
        nullptr,
        IID_PPV_ARGS(&m_denoisedOutput)
    );
    if (FAILED(hr)) return false;
    
    std::cout << "DLSS-D resources created successfully" << std::endl;
    return true;
}

bool DLSSDDemo::InitializeDLSSD() {
    std::cout << "Initializing DLSS-D..." << std::endl;

    // Check DLSS-D support
    sl::FeatureRequirements requirements = {};
    sl::Result result = sl::slIsFeatureSupported(sl::kFeatureDLSS_D, requirements);
    if (result != sl::eOk) {
        std::cerr << "DLSS-D is not supported: " << (int)result << std::endl;
        return false;
    }

    std::cout << "DLSS-D is supported!" << std::endl;

    // Configure DLSS-D options
    m_dlssdOptions.mode = sl::eDLSSDModeBalanced;
    m_dlssdOptions.preset = sl::DLSSDPreset::ePresetE; // Latest transformer model
    m_dlssdOptions.outputWidth = m_width;
    m_dlssdOptions.outputHeight = m_height;

    result = sl::slDLSSDSetOptions(sl::ViewportHandle(0), m_dlssdOptions);
    if (result != sl::eOk) {
        std::cerr << "Failed to set DLSS-D options: " << (int)result << std::endl;
        return false;
    }

    m_dlssdInitialized = true;
    std::cout << "DLSS-D initialized successfully" << std::endl;
    std::cout << "Using preset: " << (int)m_dlssdOptions.preset << std::endl;
    std::cout << "Mode: " << (int)m_dlssdOptions.mode << std::endl;
    return true;
}

void DLSSDDemo::Update() {
    // Update game logic here
    static UINT frameCount = 0;
    frameCount++;

    if (frameCount % 60 == 0) {
        std::cout << "Frame " << frameCount << " - DLSS-D denoising active" << std::endl;
    }
}

void DLSSDDemo::Render() {
    PopulateCommandList();

    // Execute command list
    ID3D12CommandList* ppCommandLists[] = { m_commandList.Get() };
    m_commandQueue->ExecuteCommandLists(_countof(ppCommandLists), ppCommandLists);

    Present();
    WaitForPreviousFrame();
}

void DLSSDDemo::PopulateCommandList() {
    // Reset command list
    m_commandAllocator->Reset();
    m_commandList->Reset(m_commandAllocator.Get(), nullptr);

    // Simulate rendering noisy ray-traced scene
    RenderNoisyScene();

    // Evaluate DLSS-D for denoising
    if (m_dlssdInitialized) {
        EvaluateDLSSD();
    }

    // Copy denoised output to back buffer
    CD3DX12_RESOURCE_BARRIER barrier = CD3DX12_RESOURCE_BARRIER::Transition(
        m_renderTargets[m_frameIndex].Get(),
        D3D12_RESOURCE_STATE_PRESENT,
        D3D12_RESOURCE_STATE_COPY_DEST
    );
    m_commandList->ResourceBarrier(1, &barrier);

    // In a real implementation, you'd copy the denoised output to the back buffer
    // For this demo, we'll just transition back to present state

    barrier = CD3DX12_RESOURCE_BARRIER::Transition(
        m_renderTargets[m_frameIndex].Get(),
        D3D12_RESOURCE_STATE_COPY_DEST,
        D3D12_RESOURCE_STATE_PRESENT
    );
    m_commandList->ResourceBarrier(1, &barrier);

    m_commandList->Close();
}

void DLSSDDemo::RenderNoisyScene() {
    // This is where you would render your ray-traced scene with noise
    // For this demo, we'll simulate the process

    std::cout << "Rendering noisy ray-traced scene..." << std::endl;

    // Simulate ray tracing that produces noisy results
    SimulateNoisyRayTracing();

    // In a real implementation, you would:
    // 1. Render G-buffer (albedo, normals, depth)
    // 2. Perform ray tracing for reflections/GI
    // 3. Generate motion vectors
    // 4. Output noisy ray-traced results
}

void DLSSDDemo::SimulateNoisyRayTracing() {
    // Simulate the generation of noisy ray-traced data
    // In a real application, this would be your ray tracing shaders

    // Clear buffers with simulated data
    const float clearColor[] = { 0.1f, 0.2f, 0.8f, 1.0f }; // Simulated noisy color
    const float clearAlbedo[] = { 0.7f, 0.7f, 0.7f, 1.0f }; // Surface albedo
    const float clearNormal[] = { 0.0f, 0.0f, 1.0f, 1.0f }; // World space normal
    const float clearDepth[] = { 0.5f, 0.0f, 0.0f, 0.0f };  // Linear depth
    const float clearMV[] = { 0.01f, 0.01f, 0.0f, 0.0f };   // Motion vectors

    // Note: In a real implementation, you would use proper render target views
    // and clear/render to these buffers with actual ray tracing results

    std::cout << "Simulated noisy ray tracing complete" << std::endl;
}

void DLSSDDemo::EvaluateDLSSD() {
    std::cout << "Evaluating DLSS-D denoiser..." << std::endl;

    // Set up DLSS-D inputs
    sl::Resource noisyInput = {};
    noisyInput.type = sl::eResourceTypeTex2d;
    noisyInput.resource = m_noisyColorBuffer.Get();
    noisyInput.state = sl::eResourceStateRenderTarget;

    sl::Resource albedoInput = {};
    albedoInput.type = sl::eResourceTypeTex2d;
    albedoInput.resource = m_albedoBuffer.Get();
    albedoInput.state = sl::eResourceStateRenderTarget;

    sl::Resource normalInput = {};
    normalInput.type = sl::eResourceTypeTex2d;
    normalInput.resource = m_normalBuffer.Get();
    normalInput.state = sl::eResourceStateRenderTarget;

    sl::Resource depthInput = {};
    depthInput.type = sl::eResourceTypeTex2d;
    depthInput.resource = m_depthBuffer.Get();
    depthInput.state = sl::eResourceStateRenderTarget;

    sl::Resource motionVectorInput = {};
    motionVectorInput.type = sl::eResourceTypeTex2d;
    motionVectorInput.resource = m_motionVectorBuffer.Get();
    motionVectorInput.state = sl::eResourceStateRenderTarget;

    sl::Resource denoisedOutput = {};
    denoisedOutput.type = sl::eResourceTypeTex2d;
    denoisedOutput.resource = m_denoisedOutput.Get();
    denoisedOutput.state = sl::eResourceStateUnorderedAccess;

    // Set up DLSS-D constants
    sl::DLSSDConstants dlssdConstants = {};
    dlssdConstants.reset = false;
    dlssdConstants.mvecScale = { 1.0f, 1.0f };
    dlssdConstants.jitterOffset = { 0.0f, 0.0f };
    dlssdConstants.preExposure = 1.0f;

    // Set resources for DLSS-D evaluation
    const sl::BaseStructure* inputs[] = {
        &noisyInput,
        &albedoInput,
        &normalInput,
        &depthInput,
        &motionVectorInput,
        &denoisedOutput,
        &dlssdConstants
    };

    // Evaluate DLSS-D
    sl::Result result = sl::slEvaluateFeature(
        sl::kFeatureDLSS_D,
        sl::ViewportHandle(0),
        m_commandList.Get(),
        inputs,
        _countof(inputs)
    );

    if (result != sl::eOk) {
        std::cerr << "DLSS-D evaluation failed: " << (int)result << std::endl;
    } else {
        std::cout << "DLSS-D denoising successful!" << std::endl;
    }
}

void DLSSDDemo::Present() {
    m_swapChain->Present(1, 0);
}

void DLSSDDemo::WaitForPreviousFrame() {
    const UINT64 fence = m_fenceValue;
    m_commandQueue->Signal(m_fence.Get(), fence);
    m_fenceValue++;

    if (m_fence->GetCompletedValue() < fence) {
        m_fence->SetEventOnCompletion(fence, m_fenceEvent);
        WaitForSingleObject(m_fenceEvent, INFINITE);
    }

    m_frameIndex = m_swapChain->GetCurrentBackBufferIndex();
}

void DLSSDDemo::Cleanup() {
    WaitForPreviousFrame();

    if (m_fenceEvent) {
        CloseHandle(m_fenceEvent);
    }

    if (m_dlssdInitialized) {
        sl::slShutdown();
        m_dlssdInitialized = false;
    }
}

// Window procedure
LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    switch (uMsg) {
        case WM_DESTROY:
            PostQuitMessage(0);
            return 0;
        case WM_KEYDOWN:
            if (wParam == VK_ESCAPE) {
                PostQuitMessage(0);
            }
            return 0;
    }
    return DefWindowProc(hwnd, uMsg, wParam, lParam);
}

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE, LPSTR, int nCmdShow) {
    // Create window
    const wchar_t CLASS_NAME[] = L"DLSSDDemo";

    WNDCLASS wc = {};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.lpszClassName = CLASS_NAME;
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);

    RegisterClass(&wc);

    HWND hwnd = CreateWindowEx(
        0,
        CLASS_NAME,
        L"DLSS-D Denoiser Demo - Press ESC to exit",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT, 1920, 1080,
        nullptr, nullptr, hInstance, nullptr
    );

    if (hwnd == nullptr) return 0;

    ShowWindow(hwnd, nCmdShow);
    UpdateWindow(hwnd);

    // Initialize demo
    DLSSDDemo demo;
    if (!demo.Initialize(hwnd)) {
        MessageBox(hwnd, L"Failed to initialize DLSS-D demo", L"Error", MB_OK);
        return -1;
    }

    std::cout << "\n=== DLSS-D Demo Running ===" << std::endl;
    std::cout << "DLSS-D is denoising ray-traced effects" << std::endl;
    std::cout << "Press ESC to exit" << std::endl;
    std::cout << "=========================" << std::endl;

    // Main loop
    MSG msg = {};
    bool running = true;

    while (running) {
        while (PeekMessage(&msg, nullptr, 0, 0, PM_REMOVE)) {
            if (msg.message == WM_QUIT) {
                running = false;
                break;
            }
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }

        if (running) {
            demo.Update();
            demo.Render();
        }
    }

    std::cout << "\nDLSS-D Demo completed!" << std::endl;
    return 0;
}
