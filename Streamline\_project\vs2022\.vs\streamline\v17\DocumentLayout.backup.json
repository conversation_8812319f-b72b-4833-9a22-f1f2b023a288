{"Version": 1, "WorkspaceRootPath": "D:\\Company\\NVIDIA_DLSS\\Streamline\\_project\\vs2022\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{CFF4666E-BBAC-A8BB-24B4-8117108B43AC}|sl.compute.vcxproj|D:\\COMPANY\\NVIDIA_DLSS\\STREAMLINE\\EXTERNAL\\REFLEX-SDK-VK\\INC\\NVLOWLATENCYVK.H||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{C8238F97-3403-0E33-3D1B-9909A9797494}|sl.dlss.vcxproj|D:\\Company\\NVIDIA_DLSS\\Streamline\\source\\plugins\\sl.dlss\\dlssEntry.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{C8238F97-3403-0E33-3D1B-9909A9797494}|sl.dlss.vcxproj|D:\\COMPANY\\NVIDIA_DLSS\\STREAMLINE\\_ARTIFACTS\\SHADERS\\MVEC_SPV.H||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{6B42E7B7-D7AC-9B2E-20EB-4CD48C947983}|sl.dlss_d.vcxproj|D:\\Company\\NVIDIA_DLSS\\Streamline\\source\\core\\sl.api\\internal.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{9DB872E6-0979-EE1C-D2B3-379A3E3333F9}|sl.interposer.vcxproj|D:\\COMPANY\\NVIDIA_DLSS\\STREAMLINE\\EXTERNAL\\NVAPI\\NVAPI_LITE_COMMON.H||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{9DB872E6-0979-EE1C-D2B3-379A3E3333F9}|sl.interposer.vcxproj|D:\\Company\\NVIDIA_DLSS\\Streamline\\include\\sl_core_types.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{9DB872E6-0979-EE1C-D2B3-379A3E3333F9}|sl.interposer.vcxproj|D:\\COMPANY\\NVIDIA_DLSS\\STREAMLINE\\EXTERNAL\\NVAPI\\NVAPI_LITE_SALSTART.H||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "NvLowLatencyVk.h", "DocumentMoniker": "D:\\Company\\NVIDIA_DLSS\\Streamline\\external\\reflex-sdk-vk\\inc\\NvLowLatencyVk.h", "RelativeDocumentMoniker": "..\\..\\external\\reflex-sdk-vk\\inc\\NvLowLatencyVk.h", "ToolTip": "D:\\Company\\NVIDIA_DLSS\\Streamline\\external\\reflex-sdk-vk\\inc\\NvLowLatencyVk.h", "RelativeToolTip": "..\\..\\external\\reflex-sdk-vk\\inc\\NvLowLatencyVk.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-08-13T06:30:24.873Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "mvec_spv.h", "DocumentMoniker": "D:\\Company\\NVIDIA_DLSS\\Streamline\\_artifacts\\shaders\\mvec_spv.h", "RelativeDocumentMoniker": "..\\..\\_artifacts\\shaders\\mvec_spv.h", "ToolTip": "D:\\Company\\NVIDIA_DLSS\\Streamline\\_artifacts\\shaders\\mvec_spv.h", "RelativeToolTip": "..\\..\\_artifacts\\shaders\\mvec_spv.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-08-13T06:27:26.979Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "dlssEntry.cpp", "DocumentMoniker": "D:\\Company\\NVIDIA_DLSS\\Streamline\\source\\plugins\\sl.dlss\\dlssEntry.cpp", "RelativeDocumentMoniker": "..\\..\\source\\plugins\\sl.dlss\\dlssEntry.cpp", "ToolTip": "D:\\Company\\NVIDIA_DLSS\\Streamline\\source\\plugins\\sl.dlss\\dlssEntry.cpp", "RelativeToolTip": "..\\..\\source\\plugins\\sl.dlss\\dlssEntry.cpp", "ViewState": "AgIAAPIAAAAAAAAAAAAAAFgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-13T06:24:27.244Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "internal.h", "DocumentMoniker": "D:\\Company\\NVIDIA_DLSS\\Streamline\\source\\core\\sl.api\\internal.h", "RelativeDocumentMoniker": "..\\..\\source\\core\\sl.api\\internal.h", "ToolTip": "D:\\Company\\NVIDIA_DLSS\\Streamline\\source\\core\\sl.api\\internal.h", "RelativeToolTip": "..\\..\\source\\core\\sl.api\\internal.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAABKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-08-13T06:22:25.13Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "nvapi_lite_common.h", "DocumentMoniker": "D:\\Company\\NVIDIA_DLSS\\Streamline\\external\\nvapi\\nvapi_lite_common.h", "RelativeDocumentMoniker": "..\\..\\external\\nvapi\\nvapi_lite_common.h", "ToolTip": "D:\\Company\\NVIDIA_DLSS\\Streamline\\external\\nvapi\\nvapi_lite_common.h", "RelativeToolTip": "..\\..\\external\\nvapi\\nvapi_lite_common.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAByAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-08-13T06:19:09.476Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "nvapi_lite_salstart.h", "DocumentMoniker": "D:\\Company\\NVIDIA_DLSS\\Streamline\\external\\nvapi\\nvapi_lite_salstart.h", "RelativeDocumentMoniker": "..\\..\\external\\nvapi\\nvapi_lite_salstart.h", "ToolTip": "D:\\Company\\NVIDIA_DLSS\\Streamline\\external\\nvapi\\nvapi_lite_salstart.h", "RelativeToolTip": "..\\..\\external\\nvapi\\nvapi_lite_salstart.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAABwAAACIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-08-13T06:15:41.704Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "sl_core_types.h", "DocumentMoniker": "D:\\Company\\NVIDIA_DLSS\\Streamline\\include\\sl_core_types.h", "RelativeDocumentMoniker": "..\\..\\include\\sl_core_types.h", "ToolTip": "D:\\Company\\NVIDIA_DLSS\\Streamline\\include\\sl_core_types.h", "RelativeToolTip": "..\\..\\include\\sl_core_types.h", "ViewState": "AgIAAFQAAAAAAAAAAAAAAD4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-08-13T06:11:21.25Z", "EditorCaption": ""}]}]}]}