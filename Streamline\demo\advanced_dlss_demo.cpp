/*
* Advanced NVIDIA DLSS Streamline Demo
* 
* This demo shows a more complete integration of DLSS with actual rendering,
* including proper resource management and DLSS evaluation.
*/

#include <iostream>
#include <windows.h>
#include <d3d12.h>
#include <dxgi1_6.h>
#include <wrl/client.h>
#include <vector>
#include <memory>

// Streamline headers
#include "../include/sl.h"
#include "../include/sl_dlss.h"
#include "../include/sl_consts.h"
#include "../include/sl_helpers.h"

using Microsoft::WRL::ComPtr;

class AdvancedDLSSDemo {
private:
    // D3D12 Core objects
    ComPtr<ID3D12Device> m_device;
    ComPtr<IDXGISwapChain3> m_swapChain;
    ComPtr<ID3D12CommandQueue> m_commandQueue;
    ComPtr<ID3D12CommandAllocator> m_commandAllocator;
    ComPtr<ID3D12GraphicsCommandList> m_commandList;
    ComPtr<ID3D12Fence> m_fence;
    HANDLE m_fenceEvent;
    UINT64 m_fenceValue;
    
    // Render targets
    static const UINT FrameCount = 2;
    ComPtr<ID3D12Resource> m_renderTargets[FrameCount];
    ComPtr<ID3D12DescriptorHeap> m_rtvHeap;
    UINT m_rtvDescriptorSize;
    UINT m_frameIndex;
    
    // DLSS resources
    ComPtr<ID3D12Resource> m_colorBuffer;
    ComPtr<ID3D12Resource> m_depthBuffer;
    ComPtr<ID3D12Resource> m_motionVectorBuffer;
    ComPtr<ID3D12Resource> m_outputBuffer;
    
    // Demo settings
    UINT m_renderWidth = 1280;   // Internal render resolution
    UINT m_renderHeight = 720;
    UINT m_displayWidth = 1920;  // Display resolution
    UINT m_displayHeight = 1080;
    
    bool m_dlssInitialized = false;
    sl::DLSSOptions m_dlssOptions = {};
    
    HWND m_hwnd = nullptr;
    
public:
    AdvancedDLSSDemo() = default;
    ~AdvancedDLSSDemo() { Cleanup(); }
    
    bool Initialize(HWND hwnd);
    bool InitializeDLSS();
    void Update();
    void Render();
    void Present();
    void Cleanup();
    
private:
    bool CreateD3D12Device();
    bool CreateSwapChain();
    bool CreateRenderTargets();
    bool CreateDLSSResources();
    bool InitializeStreamline();
    void WaitForPreviousFrame();
    void PopulateCommandList();
    void RenderScene();
    void EvaluateDLSS();
    
    static void LogCallback(sl::LogLevel level, const char* message);
};

void AdvancedDLSSDemo::LogCallback(sl::LogLevel level, const char* message) {
    const char* levelStr = "UNKNOWN";
    switch (level) {
        case sl::eLogLevelOff: return;
        case sl::eLogLevelDefault: levelStr = "DEFAULT"; break;
        case sl::eLogLevelVerbose: levelStr = "VERBOSE"; break;
        case sl::eLogLevelInfo: levelStr = "INFO"; break;
        case sl::eLogLevelWarn: levelStr = "WARN"; break;
        case sl::eLogLevelError: levelStr = "ERROR"; break;
    }
    std::cout << "[SL " << levelStr << "] " << message << std::endl;
}

bool AdvancedDLSSDemo::Initialize(HWND hwnd) {
    m_hwnd = hwnd;
    
    std::cout << "Initializing Advanced DLSS Demo..." << std::endl;
    std::cout << "Render Resolution: " << m_renderWidth << "x" << m_renderHeight << std::endl;
    std::cout << "Display Resolution: " << m_displayWidth << "x" << m_displayHeight << std::endl;
    
    if (!CreateD3D12Device()) {
        std::cerr << "Failed to create D3D12 device" << std::endl;
        return false;
    }
    
    if (!CreateSwapChain()) {
        std::cerr << "Failed to create swap chain" << std::endl;
        return false;
    }
    
    if (!CreateRenderTargets()) {
        std::cerr << "Failed to create render targets" << std::endl;
        return false;
    }
    
    if (!InitializeStreamline()) {
        std::cerr << "Failed to initialize Streamline" << std::endl;
        return false;
    }
    
    if (!CreateDLSSResources()) {
        std::cerr << "Failed to create DLSS resources" << std::endl;
        return false;
    }
    
    if (!InitializeDLSS()) {
        std::cerr << "Failed to initialize DLSS" << std::endl;
        return false;
    }
    
    std::cout << "Advanced DLSS Demo initialized successfully!" << std::endl;
    return true;
}

bool AdvancedDLSSDemo::CreateD3D12Device() {
    std::cout << "Creating D3D12 device..." << std::endl;
    
#ifdef _DEBUG
    ComPtr<ID3D12Debug> debugController;
    if (SUCCEEDED(D3D12GetDebugInterface(IID_PPV_ARGS(&debugController)))) {
        debugController->EnableDebugLayer();
    }
#endif
    
    ComPtr<IDXGIFactory4> factory;
    HRESULT hr = CreateDXGIFactory1(IID_PPV_ARGS(&factory));
    if (FAILED(hr)) return false;
    
    ComPtr<IDXGIAdapter1> adapter;
    for (UINT adapterIndex = 0; DXGI_ERROR_NOT_FOUND != factory->EnumAdapters1(adapterIndex, &adapter); ++adapterIndex) {
        DXGI_ADAPTER_DESC1 desc;
        adapter->GetDesc1(&desc);
        
        if (desc.Flags & DXGI_ADAPTER_FLAG_SOFTWARE) continue;
        
        hr = D3D12CreateDevice(adapter.Get(), D3D_FEATURE_LEVEL_11_0, IID_PPV_ARGS(&m_device));
        if (SUCCEEDED(hr)) {
            std::wcout << L"Using adapter: " << desc.Description << std::endl;
            break;
        }
    }
    
    if (!m_device) return false;
    
    // Create command queue
    D3D12_COMMAND_QUEUE_DESC queueDesc = {};
    queueDesc.Flags = D3D12_COMMAND_QUEUE_FLAG_NONE;
    queueDesc.Type = D3D12_COMMAND_LIST_TYPE_DIRECT;
    
    hr = m_device->CreateCommandQueue(&queueDesc, IID_PPV_ARGS(&m_commandQueue));
    if (FAILED(hr)) return false;
    
    // Create command allocator
    hr = m_device->CreateCommandAllocator(D3D12_COMMAND_LIST_TYPE_DIRECT, IID_PPV_ARGS(&m_commandAllocator));
    if (FAILED(hr)) return false;
    
    // Create command list
    hr = m_device->CreateCommandList(0, D3D12_COMMAND_LIST_TYPE_DIRECT, m_commandAllocator.Get(), nullptr, IID_PPV_ARGS(&m_commandList));
    if (FAILED(hr)) return false;
    
    m_commandList->Close();
    
    // Create synchronization objects
    hr = m_device->CreateFence(0, D3D12_FENCE_FLAG_NONE, IID_PPV_ARGS(&m_fence));
    if (FAILED(hr)) return false;
    
    m_fenceValue = 1;
    m_fenceEvent = CreateEvent(nullptr, FALSE, FALSE, nullptr);
    if (m_fenceEvent == nullptr) return false;
    
    return true;
}

bool AdvancedDLSSDemo::CreateSwapChain() {
    std::cout << "Creating swap chain..." << std::endl;
    
    ComPtr<IDXGIFactory4> factory;
    HRESULT hr = CreateDXGIFactory1(IID_PPV_ARGS(&factory));
    if (FAILED(hr)) return false;
    
    DXGI_SWAP_CHAIN_DESC1 swapChainDesc = {};
    swapChainDesc.BufferCount = FrameCount;
    swapChainDesc.Width = m_displayWidth;
    swapChainDesc.Height = m_displayHeight;
    swapChainDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    swapChainDesc.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    swapChainDesc.SwapEffect = DXGI_SWAP_EFFECT_FLIP_DISCARD;
    swapChainDesc.SampleDesc.Count = 1;
    
    ComPtr<IDXGISwapChain1> swapChain;
    hr = factory->CreateSwapChainForHwnd(
        m_commandQueue.Get(),
        m_hwnd,
        &swapChainDesc,
        nullptr,
        nullptr,
        &swapChain
    );
    
    if (FAILED(hr)) return false;
    
    hr = swapChain.As(&m_swapChain);
    if (FAILED(hr)) return false;
    
    m_frameIndex = m_swapChain->GetCurrentBackBufferIndex();
    
    return true;
}

bool AdvancedDLSSDemo::CreateRenderTargets() {
    std::cout << "Creating render targets..." << std::endl;
    
    // Create descriptor heap for RTV
    D3D12_DESCRIPTOR_HEAP_DESC rtvHeapDesc = {};
    rtvHeapDesc.NumDescriptors = FrameCount;
    rtvHeapDesc.Type = D3D12_DESCRIPTOR_HEAP_TYPE_RTV;
    rtvHeapDesc.Flags = D3D12_DESCRIPTOR_HEAP_FLAG_NONE;
    
    HRESULT hr = m_device->CreateDescriptorHeap(&rtvHeapDesc, IID_PPV_ARGS(&m_rtvHeap));
    if (FAILED(hr)) return false;
    
    m_rtvDescriptorSize = m_device->GetDescriptorHandleIncrementSize(D3D12_DESCRIPTOR_HEAP_TYPE_RTV);
    
    // Create frame resources
    CD3DX12_CPU_DESCRIPTOR_HANDLE rtvHandle(m_rtvHeap->GetCPUDescriptorHandleForHeapStart());
    
    for (UINT n = 0; n < FrameCount; n++) {
        hr = m_swapChain->GetBuffer(n, IID_PPV_ARGS(&m_renderTargets[n]));
        if (FAILED(hr)) return false;
        
        m_device->CreateRenderTargetView(m_renderTargets[n].Get(), nullptr, rtvHandle);
        rtvHandle.Offset(1, m_rtvDescriptorSize);
    }
    
    return true;
}

bool AdvancedDLSSDemo::InitializeStreamline() {
    std::cout << "Initializing Streamline..." << std::endl;
    
    sl::Preferences pref = {};
    pref.showConsole = true;
    pref.logLevel = sl::eLogLevelInfo;
    pref.pathsToPlugins = nullptr;
    pref.numPathsToPlugins = 0;
    pref.pathToLogsAndData = nullptr;
    pref.logMessageCallback = LogCallback;
    pref.applicationId = 0; // Get from NVIDIA
    pref.engineType = sl::eEngineTypeCustom;
    pref.engineVersion = "1.0.0";
    pref.projectId = "AdvancedDLSSDemo";
    
    sl::Result result = sl::slInit(pref);
    if (result != sl::eOk) {
        std::cerr << "Failed to initialize Streamline: " << (int)result << std::endl;
        return false;
    }
    
    return true;
}

bool AdvancedDLSSDemo::CreateDLSSResources() {
    std::cout << "Creating DLSS resources..." << std::endl;
    
    // Create color buffer (render target)
    D3D12_RESOURCE_DESC colorDesc = {};
    colorDesc.Dimension = D3D12_RESOURCE_DIMENSION_TEXTURE2D;
    colorDesc.Width = m_renderWidth;
    colorDesc.Height = m_renderHeight;
    colorDesc.DepthOrArraySize = 1;
    colorDesc.MipLevels = 1;
    colorDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    colorDesc.SampleDesc.Count = 1;
    colorDesc.Flags = D3D12_RESOURCE_FLAG_ALLOW_RENDER_TARGET;
    
    CD3DX12_HEAP_PROPERTIES heapProps(D3D12_HEAP_TYPE_DEFAULT);
    HRESULT hr = m_device->CreateCommittedResource(
        &heapProps,
        D3D12_HEAP_FLAG_NONE,
        &colorDesc,
        D3D12_RESOURCE_STATE_RENDER_TARGET,
        nullptr,
        IID_PPV_ARGS(&m_colorBuffer)
    );
    if (FAILED(hr)) return false;
    
    // Create depth buffer
    D3D12_RESOURCE_DESC depthDesc = colorDesc;
    depthDesc.Format = DXGI_FORMAT_D32_FLOAT;
    depthDesc.Flags = D3D12_RESOURCE_FLAG_ALLOW_DEPTH_STENCIL;
    
    D3D12_CLEAR_VALUE depthClearValue = {};
    depthClearValue.Format = DXGI_FORMAT_D32_FLOAT;
    depthClearValue.DepthStencil.Depth = 1.0f;
    
    hr = m_device->CreateCommittedResource(
        &heapProps,
        D3D12_HEAP_FLAG_NONE,
        &depthDesc,
        D3D12_RESOURCE_STATE_DEPTH_WRITE,
        &depthClearValue,
        IID_PPV_ARGS(&m_depthBuffer)
    );
    if (FAILED(hr)) return false;
    
    // Create motion vector buffer
    D3D12_RESOURCE_DESC mvDesc = colorDesc;
    mvDesc.Format = DXGI_FORMAT_R16G16_FLOAT;
    
    hr = m_device->CreateCommittedResource(
        &heapProps,
        D3D12_HEAP_FLAG_NONE,
        &mvDesc,
        D3D12_RESOURCE_STATE_RENDER_TARGET,
        nullptr,
        IID_PPV_ARGS(&m_motionVectorBuffer)
    );
    if (FAILED(hr)) return false;
    
    // Create output buffer
    D3D12_RESOURCE_DESC outputDesc = {};
    outputDesc.Dimension = D3D12_RESOURCE_DIMENSION_TEXTURE2D;
    outputDesc.Width = m_displayWidth;
    outputDesc.Height = m_displayHeight;
    outputDesc.DepthOrArraySize = 1;
    outputDesc.MipLevels = 1;
    outputDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    outputDesc.SampleDesc.Count = 1;
    outputDesc.Flags = D3D12_RESOURCE_FLAG_ALLOW_UNORDERED_ACCESS;
    
    hr = m_device->CreateCommittedResource(
        &heapProps,
        D3D12_HEAP_FLAG_NONE,
        &outputDesc,
        D3D12_RESOURCE_STATE_UNORDERED_ACCESS,
        nullptr,
        IID_PPV_ARGS(&m_outputBuffer)
    );
    if (FAILED(hr)) return false;
    
    return true;
}

bool AdvancedDLSSDemo::InitializeDLSS() {
    std::cout << "Initializing DLSS..." << std::endl;
    
    // Check DLSS support
    sl::FeatureRequirements requirements = {};
    sl::Result result = sl::slIsFeatureSupported(sl::kFeatureDLSS, requirements);
    if (result != sl::eOk) {
        std::cerr << "DLSS is not supported: " << (int)result << std::endl;
        return false;
    }
    
    std::cout << "DLSS is supported!" << std::endl;
    
    // Configure DLSS options
    m_dlssOptions.mode = sl::eDLSSModeBalanced;
    m_dlssOptions.outputWidth = m_displayWidth;
    m_dlssOptions.outputHeight = m_displayHeight;
    m_dlssOptions.colorBuffersHDR = false;
    m_dlssOptions.useAutoExposure = false;
    m_dlssOptions.sharpness = 0.0f;
    
    result = sl::slDLSSSetOptions(sl::ViewportHandle(0), m_dlssOptions);
    if (result != sl::eOk) {
        std::cerr << "Failed to set DLSS options: " << (int)result << std::endl;
        return false;
    }
    
    m_dlssInitialized = true;
    std::cout << "DLSS initialized successfully" << std::endl;
    return true;
}

void AdvancedDLSSDemo::Update() {
    // Update game logic here
    // For this demo, we'll just increment frame counter
    static UINT frameCount = 0;
    frameCount++;
}

void AdvancedDLSSDemo::Render() {
    PopulateCommandList();
    
    // Execute command list
    ID3D12CommandList* ppCommandLists[] = { m_commandList.Get() };
    m_commandQueue->ExecuteCommandLists(_countof(ppCommandLists), ppCommandLists);
    
    Present();
    WaitForPreviousFrame();
}

void AdvancedDLSSDemo::PopulateCommandList() {
    // Reset command list
    m_commandAllocator->Reset();
    m_commandList->Reset(m_commandAllocator.Get(), nullptr);
    
    // Render scene at lower resolution
    RenderScene();
    
    // Evaluate DLSS
    if (m_dlssInitialized) {
        EvaluateDLSS();
    }
    
    // Copy DLSS output to back buffer
    CD3DX12_RESOURCE_BARRIER barrier = CD3DX12_RESOURCE_BARRIER::Transition(
        m_renderTargets[m_frameIndex].Get(),
        D3D12_RESOURCE_STATE_PRESENT,
        D3D12_RESOURCE_STATE_COPY_DEST
    );
    m_commandList->ResourceBarrier(1, &barrier);
    
    // Copy output buffer to back buffer (simplified)
    // In a real implementation, you'd use a proper copy or blit operation
    
    barrier = CD3DX12_RESOURCE_BARRIER::Transition(
        m_renderTargets[m_frameIndex].Get(),
        D3D12_RESOURCE_STATE_COPY_DEST,
        D3D12_RESOURCE_STATE_PRESENT
    );
    m_commandList->ResourceBarrier(1, &barrier);
    
    m_commandList->Close();
}

void AdvancedDLSSDemo::RenderScene() {
    // This is where you would render your actual scene
    // For this demo, we'll just clear the buffers
    
    // Clear color buffer
    const float clearColor[] = { 0.2f, 0.4f, 0.8f, 1.0f };
    // m_commandList->ClearRenderTargetView(colorRTV, clearColor, 0, nullptr);
    
    // Clear depth buffer
    // m_commandList->ClearDepthStencilView(depthDSV, D3D12_CLEAR_FLAG_DEPTH, 1.0f, 0, 0, nullptr);
    
    std::cout << "Rendering scene at " << m_renderWidth << "x" << m_renderHeight << std::endl;
}

void AdvancedDLSSDemo::EvaluateDLSS() {
    std::cout << "Evaluating DLSS..." << std::endl;
    
    // Set up DLSS inputs
    sl::Resource colorInput = {};
    colorInput.type = sl::eResourceTypeTex2d;
    colorInput.resource = m_colorBuffer.Get();
    colorInput.state = sl::eResourceStateRenderTarget;
    
    sl::Resource depthInput = {};
    depthInput.type = sl::eResourceTypeTex2d;
    depthInput.resource = m_depthBuffer.Get();
    depthInput.state = sl::eResourceStateDepthRead;
    
    sl::Resource motionVectorInput = {};
    motionVectorInput.type = sl::eResourceTypeTex2d;
    motionVectorInput.resource = m_motionVectorBuffer.Get();
    motionVectorInput.state = sl::eResourceStateRenderTarget;
    
    sl::Resource output = {};
    output.type = sl::eResourceTypeTex2d;
    output.resource = m_outputBuffer.Get();
    output.state = sl::eResourceStateUnorderedAccess;
    
    // Set up DLSS constants
    sl::DLSSConstants dlssConstants = {};
    dlssConstants.colorBuffersHDR = false;
    dlssConstants.reset = false;
    dlssConstants.mvecScale = { 1.0f, 1.0f };
    dlssConstants.jitterOffset = { 0.0f, 0.0f };
    
    // Set resources
    const sl::BaseStructure* inputs[] = {
        &colorInput,
        &depthInput,
        &motionVectorInput,
        &output,
        &dlssConstants
    };
    
    // Evaluate DLSS
    sl::Result result = sl::slEvaluateFeature(
        sl::kFeatureDLSS,
        sl::ViewportHandle(0),
        m_commandList.Get(),
        inputs,
        _countof(inputs)
    );
    
    if (result != sl::eOk) {
        std::cerr << "DLSS evaluation failed: " << (int)result << std::endl;
    } else {
        std::cout << "DLSS evaluation successful" << std::endl;
    }
}

void AdvancedDLSSDemo::Present() {
    m_swapChain->Present(1, 0);
}

void AdvancedDLSSDemo::WaitForPreviousFrame() {
    const UINT64 fence = m_fenceValue;
    m_commandQueue->Signal(m_fence.Get(), fence);
    m_fenceValue++;
    
    if (m_fence->GetCompletedValue() < fence) {
        m_fence->SetEventOnCompletion(fence, m_fenceEvent);
        WaitForSingleObject(m_fenceEvent, INFINITE);
    }
    
    m_frameIndex = m_swapChain->GetCurrentBackBufferIndex();
}

void AdvancedDLSSDemo::Cleanup() {
    WaitForPreviousFrame();
    
    if (m_fenceEvent) {
        CloseHandle(m_fenceEvent);
    }
    
    if (m_dlssInitialized) {
        sl::slShutdown();
        m_dlssInitialized = false;
    }
}

// Window procedure
LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    switch (uMsg) {
        case WM_DESTROY:
            PostQuitMessage(0);
            return 0;
    }
    return DefWindowProc(hwnd, uMsg, wParam, lParam);
}

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE, LPSTR, int nCmdShow) {
    // Create window
    const wchar_t CLASS_NAME[] = L"AdvancedDLSSDemo";
    
    WNDCLASS wc = {};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.lpszClassName = CLASS_NAME;
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
    
    RegisterClass(&wc);
    
    HWND hwnd = CreateWindowEx(
        0,
        CLASS_NAME,
        L"Advanced DLSS Demo",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT, 1920, 1080,
        nullptr, nullptr, hInstance, nullptr
    );
    
    if (hwnd == nullptr) return 0;
    
    ShowWindow(hwnd, nCmdShow);
    
    // Initialize demo
    AdvancedDLSSDemo demo;
    if (!demo.Initialize(hwnd)) {
        MessageBox(hwnd, L"Failed to initialize demo", L"Error", MB_OK);
        return -1;
    }
    
    // Main loop
    MSG msg = {};
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
        
        demo.Update();
        demo.Render();
    }
    
    return 0;
}
