#!/usr/bin/env python3
"""
DLSS视频补帧使用示例
演示如何使用DLSS技术进行视频帧插值
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dlss_video_interpolation import VideoProcessor, check_system_requirements
from dlss_integration import DLSSFrameInterpolator

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def example_basic_usage():
    """基本使用示例"""
    logger.info("=== 基本使用示例 ===")
    
    # 检查系统要求
    check_system_requirements()
    
    # 示例参数
    input_video = "sample_video.mp4"  # 请替换为实际的视频文件
    output_video = "output_60fps.mp4"
    multiplier = 2  # 2倍帧率
    
    if not os.path.exists(input_video):
        logger.warning(f"示例视频文件不存在: {input_video}")
        logger.info("请准备一个测试视频文件，或使用 --input 参数指定")
        return
    
    try:
        # 创建视频处理器
        processor = VideoProcessor(input_video, output_video, multiplier)
        
        # 处理视频
        processor.process_video(quality='balanced', gpu_id=0)
        
        logger.info(f"视频处理完成: {output_video}")
        
    except Exception as e:
        logger.error(f"处理失败: {e}")

def example_advanced_usage():
    """高级使用示例"""
    logger.info("=== 高级使用示例 ===")
    
    # 创建DLSS帧插值器
    interpolator = DLSSFrameInterpolator(device='cuda', quality='quality')
    
    # 为特定分辨率设置DLSS
    width, height = 1920, 1080
    interpolator.setup_for_resolution(width, height)
    
    logger.info(f"DLSS帧插值器已设置: {width}x{height}")
    
    try:
        # 这里可以添加更复杂的处理逻辑
        # 例如：批量处理、自定义插值算法等
        
        logger.info("高级功能演示完成")
        
    finally:
        # 清理资源
        interpolator.cleanup()

def example_batch_processing():
    """批量处理示例"""
    logger.info("=== 批量处理示例 ===")
    
    # 输入目录
    input_dir = "input_videos"
    output_dir = "output_videos"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 支持的视频格式
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv']
    
    # 查找所有视频文件
    video_files = []
    if os.path.exists(input_dir):
        for ext in video_extensions:
            video_files.extend(Path(input_dir).glob(f"*{ext}"))
            video_files.extend(Path(input_dir).glob(f"*{ext.upper()}"))
    
    if not video_files:
        logger.warning(f"在 {input_dir} 中未找到视频文件")
        return
    
    logger.info(f"找到 {len(video_files)} 个视频文件")
    
    # 批量处理
    for video_file in video_files:
        try:
            # 生成输出文件名
            output_file = Path(output_dir) / f"{video_file.stem}_60fps{video_file.suffix}"
            
            logger.info(f"处理: {video_file.name}")
            
            # 创建处理器并处理
            processor = VideoProcessor(str(video_file), str(output_file), multiplier=2)
            processor.process_video(quality='balanced', gpu_id=0)
            
            logger.info(f"完成: {output_file.name}")
            
        except Exception as e:
            logger.error(f"处理 {video_file.name} 失败: {e}")

def example_quality_comparison():
    """质量对比示例"""
    logger.info("=== 质量对比示例 ===")
    
    input_video = "sample_video.mp4"
    
    if not os.path.exists(input_video):
        logger.warning(f"示例视频文件不存在: {input_video}")
        return
    
    quality_modes = ['performance', 'balanced', 'quality']
    
    for quality in quality_modes:
        try:
            output_video = f"output_{quality}.mp4"
            
            logger.info(f"使用 {quality} 质量模式处理...")
            
            processor = VideoProcessor(input_video, output_video, multiplier=2)
            processor.process_video(quality=quality, gpu_id=0)
            
            logger.info(f"{quality} 模式处理完成: {output_video}")
            
        except Exception as e:
            logger.error(f"{quality} 模式处理失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='DLSS视频补帧使用示例')
    parser.add_argument('--example', '-e', choices=['basic', 'advanced', 'batch', 'quality'],
                       default='basic', help='运行示例类型')
    parser.add_argument('--input', '-i', help='输入视频文件路径')
    parser.add_argument('--output', '-o', help='输出视频文件路径')
    parser.add_argument('--multiplier', '-m', type=int, default=2, 
                       choices=[2, 3, 4], help='帧率倍数')
    parser.add_argument('--quality', '-q', default='balanced',
                       choices=['performance', 'balanced', 'quality'],
                       help='质量设置')
    
    args = parser.parse_args()
    
    if args.example == 'basic':
        if args.input and args.output:
            # 使用命令行参数
            try:
                processor = VideoProcessor(args.input, args.output, args.multiplier)
                processor.process_video(quality=args.quality, gpu_id=0)
                logger.info(f"视频处理完成: {args.output}")
            except Exception as e:
                logger.error(f"处理失败: {e}")
        else:
            # 运行示例
            example_basic_usage()
    
    elif args.example == 'advanced':
        example_advanced_usage()
    
    elif args.example == 'batch':
        example_batch_processing()
    
    elif args.example == 'quality':
        example_quality_comparison()

if __name__ == '__main__':
    main() 