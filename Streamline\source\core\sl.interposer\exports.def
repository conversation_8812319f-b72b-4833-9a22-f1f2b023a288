EXPORTS

	; d3d11.dll
	D3D11CreateDevice
	D3D11CreateDeviceAndSwapChain

	; d3d12.dll
	D3D12CreateDevice
	D3D12GetDebugInterface
	D3D12CreateRootSignatureDeserializer
	D3D12CreateVersionedRootSignatureDeserializer
	D3D12EnableExperimentalFeatures
	D3D12SerializeRootSignature
	D3D12SerializeVersionedRootSignature
	D3D12GetInterface
	
	; dxgi.dll
	CreateDXGIFactory
	CreateDXGIFactory1
	CreateDXGIFactory2
	DXGIGetDebugInterface1

	; vk wrapper
	vkCreateDevice
	vkCreateInstance
	vkGetInstanceProcAddr
	vkGetDeviceProcAddr
	vkEnumerateInstanceExtensionProperties
	vkEnumerateInstanceLayerProperties
	vkDestroyInstance
	vkEnumeratePhysicalDevices
	vkGetPhysicalDeviceFeatures
	vkGetPhysicalDeviceFormatProperties
	vkGetPhysicalDeviceImageFormatProperties
	vkGetPhysicalDeviceProperties
	vkGetPhysicalDeviceQueueFamilyProperties
	vkGetPhysicalDeviceMemoryProperties
	vkDestroyDevice
	vkEnumerateDeviceExtensionProperties
	vkEnumerateDeviceLayerProperties
	vkGetDeviceQueue
	vkQueueSubmit
	vkQueueWaitIdle
	vkDeviceWaitIdle
	vkAllocateMemory
	vkFreeMemory
	vkMapMemory
	vkUnmapMemory
	vkFlushMappedMemoryRanges
	vkInvalidateMappedMemoryRanges
	vkGetDeviceMemoryCommitment
	vkBindBufferMemory
	vkBindImageMemory
	vkGetBufferMemoryRequirements
	vkGetImageMemoryRequirements
	vkGetImageSparseMemoryRequirements
	vkGetPhysicalDeviceSparseImageFormatProperties
	vkQueueBindSparse
	vkCreateFence
	vkDestroyFence
	vkResetFences
	vkGetFenceStatus
	vkWaitForFences
	vkCreateSemaphore
	vkDestroySemaphore
	vkCreateEvent
	vkDestroyEvent
	vkGetEventStatus
	vkSetEvent
	vkResetEvent
	vkCreateQueryPool
	vkDestroyQueryPool
	vkGetQueryPoolResults
	vkCreateBuffer
	vkDestroyBuffer
	vkCreateBufferView
	vkDestroyBufferView
	vkCreateImage
	vkDestroyImage
	vkGetImageSubresourceLayout
	vkCreateImageView
	vkDestroyImageView
	vkCreateShaderModule
	vkDestroyShaderModule
	vkCreatePipelineCache
	vkDestroyPipelineCache
	vkGetPipelineCacheData
	vkMergePipelineCaches
	vkCreateGraphicsPipelines
	vkCreateComputePipelines
	vkDestroyPipeline
	vkCreatePipelineLayout
	vkDestroyPipelineLayout
	vkCreateSampler
	vkDestroySampler
	vkCreateDescriptorSetLayout
	vkDestroyDescriptorSetLayout
	vkCreateDescriptorPool
	vkDestroyDescriptorPool
	vkResetDescriptorPool
	vkAllocateDescriptorSets
	vkFreeDescriptorSets
	vkUpdateDescriptorSets
	vkCreateFramebuffer
	vkDestroyFramebuffer
	vkCreateRenderPass
	vkDestroyRenderPass
	vkGetRenderAreaGranularity
	vkCreateCommandPool
	vkDestroyCommandPool
	vkResetCommandPool
	vkAllocateCommandBuffers
	vkFreeCommandBuffers
	vkBeginCommandBuffer
	vkEndCommandBuffer
	vkResetCommandBuffer
	vkCmdBindPipeline
	vkCmdSetViewport
	vkCmdSetScissor
	vkCmdSetLineWidth
	vkCmdSetDepthBias
	vkCmdSetBlendConstants
	vkCmdSetDepthBounds
	vkCmdSetStencilCompareMask
	vkCmdSetStencilWriteMask
	vkCmdSetStencilReference
	vkCmdBindDescriptorSets
	vkCmdBindIndexBuffer
	vkCmdBindVertexBuffers
	vkCmdDraw
	vkCmdDrawIndexed
	vkCmdDrawIndirect
	vkCmdDrawIndexedIndirect
	vkCmdDispatch
	vkCmdDispatchIndirect
	vkCmdCopyBuffer
	vkCmdCopyImage
	vkCmdBlitImage
	vkCmdCopyBufferToImage
	vkCmdCopyImageToBuffer
	vkCmdUpdateBuffer
	vkCmdFillBuffer
	vkCmdClearColorImage
	vkCmdClearDepthStencilImage
	vkCmdClearAttachments
	vkCmdResolveImage
	vkCmdSetEvent
	vkCmdResetEvent
	vkCmdWaitEvents
	vkCmdPipelineBarrier
	vkCmdBeginQuery
	vkCmdEndQuery
	vkCmdResetQueryPool
	vkCmdWriteTimestamp
	vkCmdCopyQueryPoolResults
	vkCmdPushConstants
	vkCmdBeginRenderPass
	vkCmdNextSubpass
	vkCmdEndRenderPass
	vkCmdExecuteCommands
	vkEnumerateInstanceVersion
	vkBindBufferMemory2
	vkBindImageMemory2
	vkGetDeviceGroupPeerMemoryFeatures
	vkCmdSetDeviceMask
	vkCmdDispatchBase
	vkEnumeratePhysicalDeviceGroups
	vkGetImageMemoryRequirements2
	vkGetBufferMemoryRequirements2
	vkGetImageSparseMemoryRequirements2
	vkGetPhysicalDeviceFeatures2
	vkGetPhysicalDeviceProperties2
	vkGetPhysicalDeviceFormatProperties2
	vkGetPhysicalDeviceImageFormatProperties2
	vkGetPhysicalDeviceQueueFamilyProperties2
	vkGetPhysicalDeviceMemoryProperties2
	vkGetPhysicalDeviceSparseImageFormatProperties2
	vkTrimCommandPool
	vkGetDeviceQueue2
	vkCreateSamplerYcbcrConversion
	vkDestroySamplerYcbcrConversion
	vkCreateDescriptorUpdateTemplate
	vkDestroyDescriptorUpdateTemplate
	vkUpdateDescriptorSetWithTemplate
	vkGetPhysicalDeviceExternalBufferProperties
	vkGetPhysicalDeviceExternalFenceProperties
	vkGetPhysicalDeviceExternalSemaphoreProperties
	vkGetDescriptorSetLayoutSupport
	vkCmdDrawIndirectCount
	vkCmdDrawIndexedIndirectCount
	vkCreateRenderPass2
	vkCmdBeginRenderPass2
	vkCmdNextSubpass2
	vkCmdEndRenderPass2
	vkResetQueryPool
	vkGetSemaphoreCounterValue
	vkWaitSemaphores
	vkSignalSemaphore
	vkGetBufferDeviceAddress
	vkGetBufferOpaqueCaptureAddress
	vkGetDeviceMemoryOpaqueCaptureAddress
	vkGetPhysicalDeviceToolProperties
	vkCreatePrivateDataSlot
	vkDestroyPrivateDataSlot
	vkSetPrivateData
	vkGetPrivateData
	vkCmdSetEvent2
	vkCmdResetEvent2
	vkCmdWaitEvents2
	vkCmdPipelineBarrier2
	vkCmdWriteTimestamp2
	vkQueueSubmit2
	vkCmdCopyBuffer2
	vkCmdCopyImage2
	vkCmdCopyBufferToImage2
	vkCmdCopyImageToBuffer2
	vkCmdBlitImage2
	vkCmdResolveImage2
	vkCmdBeginRendering
	vkCmdEndRendering
	vkCmdSetCullMode
	vkCmdSetFrontFace
	vkCmdSetPrimitiveTopology
	vkCmdSetViewportWithCount
	vkCmdSetScissorWithCount
	vkCmdBindVertexBuffers2
	vkCmdSetDepthTestEnable
	vkCmdSetDepthWriteEnable
	vkCmdSetDepthCompareOp
	vkCmdSetDepthBoundsTestEnable
	vkCmdSetStencilTestEnable
	vkCmdSetStencilOp
	vkCmdSetRasterizerDiscardEnable
	vkCmdSetDepthBiasEnable
	vkCmdSetPrimitiveRestartEnable
	vkGetDeviceBufferMemoryRequirements
	vkGetDeviceImageMemoryRequirements
	vkGetDeviceImageSparseMemoryRequirements
	vkCreateSwapchainKHR
	vkDestroySwapchainKHR
	vkGetSwapchainImagesKHR
	vkAcquireNextImageKHR
	vkQueuePresentKHR
	vkGetPhysicalDeviceSurfaceCapabilitiesKHR
	vkGetPhysicalDeviceSurfaceFormatsKHR
	vkGetPhysicalDeviceSurfaceSupportKHR
	vkGetPhysicalDeviceSurfacePresentModesKHR
	vkCreateWin32SurfaceKHR
	vkDestroySurfaceKHR
	vkGetPhysicalDeviceFeatures2KHR
	vkGetPhysicalDeviceProperties2KHR
	vkGetImageMemoryRequirements2KHR

	; SL
	slInit
	slShutdown
	slIsFeatureSupported
	slIsFeatureLoaded
	slSetFeatureLoaded
	slEvaluateFeature
	slAllocateResources
	slFreeResources
	slSetTag
	slSetTagForFrame
	slGetFeatureRequirements
	slGetFeatureVersion
	slUpgradeInterface
	slSetConstants
	slGetNativeInterface
	slGetFeatureFunction
	slGetNewFrameToken
	slSetD3DDevice
	slSetVulkanInfo
