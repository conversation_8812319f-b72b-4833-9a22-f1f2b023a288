﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{5056772E-F9BC-3A41-B711-74F839BD722D}"
	ProjectSection(ProjectDependencies) = postProject
		{7D0C3948-D106-3CCA-B12D-8938BEC70356} = {7D0C3948-D106-3CCA-B12D-8938BEC70356}
		{7F57C4BC-A3BF-3CDB-B8DD-B1F655BD1E73} = {7F57C4BC-A3BF-3CDB-B8DD-B1F655BD1E73}
		{F7AFF33A-A865-3524-BD48-F6FEBBF6D0C4} = {F7AFF33A-A865-3524-BD48-F6FEBBF6D0C4}
		{CE0D5B09-5049-3662-969F-BB72EA52ED9C} = {CE0D5B09-5049-3662-969F-BB72EA52ED9C}
		{0C4FF51F-971C-336B-A104-30D31842F3D9} = {0C4FF51F-971C-336B-A104-30D31842F3D9}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "AdvancedDLSSDemo", "AdvancedDLSSDemo.vcxproj", "{7D0C3948-D106-3CCA-B12D-8938BEC70356}"
	ProjectSection(ProjectDependencies) = postProject
		{0C4FF51F-971C-336B-A104-30D31842F3D9} = {0C4FF51F-971C-336B-A104-30D31842F3D9}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "DLSSDDemo", "DLSSDDemo.vcxproj", "{7F57C4BC-A3BF-3CDB-B8DD-B1F655BD1E73}"
	ProjectSection(ProjectDependencies) = postProject
		{0C4FF51F-971C-336B-A104-30D31842F3D9} = {0C4FF51F-971C-336B-A104-30D31842F3D9}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "SimpleDLSSDDemo", "SimpleDLSSDDemo.vcxproj", "{F7AFF33A-A865-3524-BD48-F6FEBBF6D0C4}"
	ProjectSection(ProjectDependencies) = postProject
		{0C4FF51F-971C-336B-A104-30D31842F3D9} = {0C4FF51F-971C-336B-A104-30D31842F3D9}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "SimpleDLSSDemo", "SimpleDLSSDemo.vcxproj", "{CE0D5B09-5049-3662-969F-BB72EA52ED9C}"
	ProjectSection(ProjectDependencies) = postProject
		{0C4FF51F-971C-336B-A104-30D31842F3D9} = {0C4FF51F-971C-336B-A104-30D31842F3D9}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{0C4FF51F-971C-336B-A104-30D31842F3D9}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "show_usage", "show_usage.vcxproj", "{EEB9AA7C-AB66-38B3-8FE2-5E6A5AFBCC6D}"
	ProjectSection(ProjectDependencies) = postProject
		{0C4FF51F-971C-336B-A104-30D31842F3D9} = {0C4FF51F-971C-336B-A104-30D31842F3D9}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{5056772E-F9BC-3A41-B711-74F839BD722D}.Debug|x64.ActiveCfg = Debug|x64
		{5056772E-F9BC-3A41-B711-74F839BD722D}.Release|x64.ActiveCfg = Release|x64
		{5056772E-F9BC-3A41-B711-74F839BD722D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{5056772E-F9BC-3A41-B711-74F839BD722D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{7D0C3948-D106-3CCA-B12D-8938BEC70356}.Debug|x64.ActiveCfg = Debug|x64
		{7D0C3948-D106-3CCA-B12D-8938BEC70356}.Debug|x64.Build.0 = Debug|x64
		{7D0C3948-D106-3CCA-B12D-8938BEC70356}.Release|x64.ActiveCfg = Release|x64
		{7D0C3948-D106-3CCA-B12D-8938BEC70356}.Release|x64.Build.0 = Release|x64
		{7D0C3948-D106-3CCA-B12D-8938BEC70356}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{7D0C3948-D106-3CCA-B12D-8938BEC70356}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{7D0C3948-D106-3CCA-B12D-8938BEC70356}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{7D0C3948-D106-3CCA-B12D-8938BEC70356}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{7F57C4BC-A3BF-3CDB-B8DD-B1F655BD1E73}.Debug|x64.ActiveCfg = Debug|x64
		{7F57C4BC-A3BF-3CDB-B8DD-B1F655BD1E73}.Debug|x64.Build.0 = Debug|x64
		{7F57C4BC-A3BF-3CDB-B8DD-B1F655BD1E73}.Release|x64.ActiveCfg = Release|x64
		{7F57C4BC-A3BF-3CDB-B8DD-B1F655BD1E73}.Release|x64.Build.0 = Release|x64
		{7F57C4BC-A3BF-3CDB-B8DD-B1F655BD1E73}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{7F57C4BC-A3BF-3CDB-B8DD-B1F655BD1E73}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{7F57C4BC-A3BF-3CDB-B8DD-B1F655BD1E73}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{7F57C4BC-A3BF-3CDB-B8DD-B1F655BD1E73}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{F7AFF33A-A865-3524-BD48-F6FEBBF6D0C4}.Debug|x64.ActiveCfg = Debug|x64
		{F7AFF33A-A865-3524-BD48-F6FEBBF6D0C4}.Debug|x64.Build.0 = Debug|x64
		{F7AFF33A-A865-3524-BD48-F6FEBBF6D0C4}.Release|x64.ActiveCfg = Release|x64
		{F7AFF33A-A865-3524-BD48-F6FEBBF6D0C4}.Release|x64.Build.0 = Release|x64
		{F7AFF33A-A865-3524-BD48-F6FEBBF6D0C4}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{F7AFF33A-A865-3524-BD48-F6FEBBF6D0C4}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{F7AFF33A-A865-3524-BD48-F6FEBBF6D0C4}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{F7AFF33A-A865-3524-BD48-F6FEBBF6D0C4}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{CE0D5B09-5049-3662-969F-BB72EA52ED9C}.Debug|x64.ActiveCfg = Debug|x64
		{CE0D5B09-5049-3662-969F-BB72EA52ED9C}.Debug|x64.Build.0 = Debug|x64
		{CE0D5B09-5049-3662-969F-BB72EA52ED9C}.Release|x64.ActiveCfg = Release|x64
		{CE0D5B09-5049-3662-969F-BB72EA52ED9C}.Release|x64.Build.0 = Release|x64
		{CE0D5B09-5049-3662-969F-BB72EA52ED9C}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{CE0D5B09-5049-3662-969F-BB72EA52ED9C}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{CE0D5B09-5049-3662-969F-BB72EA52ED9C}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{CE0D5B09-5049-3662-969F-BB72EA52ED9C}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{0C4FF51F-971C-336B-A104-30D31842F3D9}.Debug|x64.ActiveCfg = Debug|x64
		{0C4FF51F-971C-336B-A104-30D31842F3D9}.Debug|x64.Build.0 = Debug|x64
		{0C4FF51F-971C-336B-A104-30D31842F3D9}.Release|x64.ActiveCfg = Release|x64
		{0C4FF51F-971C-336B-A104-30D31842F3D9}.Release|x64.Build.0 = Release|x64
		{0C4FF51F-971C-336B-A104-30D31842F3D9}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{0C4FF51F-971C-336B-A104-30D31842F3D9}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{0C4FF51F-971C-336B-A104-30D31842F3D9}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{0C4FF51F-971C-336B-A104-30D31842F3D9}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{EEB9AA7C-AB66-38B3-8FE2-5E6A5AFBCC6D}.Debug|x64.ActiveCfg = Debug|x64
		{EEB9AA7C-AB66-38B3-8FE2-5E6A5AFBCC6D}.Release|x64.ActiveCfg = Release|x64
		{EEB9AA7C-AB66-38B3-8FE2-5E6A5AFBCC6D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{EEB9AA7C-AB66-38B3-8FE2-5E6A5AFBCC6D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {886D002C-A214-330F-9352-7A50367782C4}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
