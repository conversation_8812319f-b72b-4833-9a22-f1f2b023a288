@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    DLSS Demo Environment Setup
echo ========================================
echo.

:: Check if we're in the right directory
if not exist "simple_dlss_demo.cpp" (
    echo Error: Demo files not found!
    echo Please run this script from the Streamline\demo directory.
    pause
    exit /b 1
)

:: Create necessary directories
echo Creating directory structure...
if not exist "..\bin" mkdir "..\bin"
if not exist "..\bin\x64" mkdir "..\bin\x64"
if not exist "..\bin\x64\development" mkdir "..\bin\x64\development"
if not exist "..\lib" mkdir "..\lib"
if not exist "..\lib\x64" mkdir "..\lib\x64"

echo.
echo ========================================
echo    Checking for Streamline Binaries
echo ========================================

:: Check for built artifacts
set "ARTIFACTS_DIR=..\_artifacts"
set "BIN_DIR=..\bin\x64"
set "LIB_DIR=..\lib\x64"

echo Checking for built Streamline artifacts...

:: List of required DLLs and their sources
set "DLLS_TO_COPY="
set "FOUND_DLLS=0"

:: Check for sl.interposer
if exist "%ARTIFACTS_DIR%\sl.interposer\Debug_x64\sl.interposer.dll" (
    echo [FOUND] sl.interposer.dll in Debug artifacts
    copy "%ARTIFACTS_DIR%\sl.interposer\Debug_x64\sl.interposer.dll" "%BIN_DIR%\" >nul 2>&1
    if exist "%ARTIFACTS_DIR%\sl.interposer\Debug_x64\sl.interposer.lib" (
        copy "%ARTIFACTS_DIR%\sl.interposer\Debug_x64\sl.interposer.lib" "%LIB_DIR%\" >nul 2>&1
    )
    set /a FOUND_DLLS+=1
) else (
    echo [MISSING] sl.interposer.dll not found in artifacts
)

:: Check for sl.common
if exist "%ARTIFACTS_DIR%\sl.common\Debug_x64\sl.common.dll" (
    echo [FOUND] sl.common.dll in Debug artifacts
    copy "%ARTIFACTS_DIR%\sl.common\Debug_x64\sl.common.dll" "%BIN_DIR%\" >nul 2>&1
    set /a FOUND_DLLS+=1
) else (
    echo [MISSING] sl.common.dll not found in artifacts
)

:: Check for sl.dlss
if exist "%ARTIFACTS_DIR%\sl.dlss\Debug_x64\sl.dlss.dll" (
    echo [FOUND] sl.dlss.dll in Debug artifacts
    copy "%ARTIFACTS_DIR%\sl.dlss\Debug_x64\sl.dlss.dll" "%BIN_DIR%\" >nul 2>&1
    set /a FOUND_DLLS+=1
) else (
    echo [MISSING] sl.dlss.dll not found in artifacts
)

echo.
echo Found %FOUND_DLLS% Streamline DLLs

:: Copy JSON configuration files
echo.
echo Copying configuration files...
if exist "..\scripts\sl.interposer.json" (
    copy "..\scripts\sl.interposer.json" "%BIN_DIR%\" >nul 2>&1
    echo [COPIED] sl.interposer.json
)

if exist "..\scripts\sl.common.json" (
    copy "..\scripts\sl.common.json" "%BIN_DIR%\" >nul 2>&1
    echo [COPIED] sl.common.json
)

echo.
echo ========================================
echo    DLSS Runtime Requirements
echo ========================================
echo.
echo The demo also requires DLSS runtime DLLs:
echo - nvngx_dlss.dll (DLSS runtime library)
echo.
echo These can be obtained from:
echo 1. NVIDIA DLSS SDK download
echo 2. Pre-built Streamline SDK package
echo 3. Games that include DLSS support
echo.

:: Check for DLSS runtime
if exist "%BIN_DIR%\nvngx_dlss.dll" (
    echo [FOUND] nvngx_dlss.dll
) else (
    echo [MISSING] nvngx_dlss.dll - You need to obtain this separately
    echo.
    echo To get nvngx_dlss.dll:
    echo 1. Download DLSS SDK from: https://developer.nvidia.com/rtx/dlss
    echo 2. Extract and copy nvngx_dlss.dll to: %BIN_DIR%\
    echo 3. Or copy from a game installation that includes DLSS
)

echo.
echo ========================================
echo    Build Instructions
echo ========================================
echo.

if %FOUND_DLLS% GEQ 3 (
    echo ✓ Streamline DLLs found! You can now build and run the demos.
    echo.
    echo To build the demos:
    echo 1. Run: build_demo.bat
    echo 2. Or manually: cd build ^&^& cmake .. ^&^& cmake --build .
    echo.
    echo To run:
    echo - Simple Demo: build\bin\Debug\SimpleDLSSDemo.exe
    echo - Advanced Demo: build\bin\Debug\AdvancedDLSSDemo.exe
) else (
    echo ⚠ Missing Streamline DLLs. You need to either:
    echo.
    echo Option 1 - Build Streamline from source:
    echo   cd ..
    echo   build.bat -debug
    echo   cd demo
    echo   setup_demo_environment.bat
    echo.
    echo Option 2 - Download pre-built Streamline SDK:
    echo   1. Download from NVIDIA Developer website
    echo   2. Extract to appropriate directories
    echo   3. Re-run this script
    echo.
    echo Option 3 - Use package script (if available):
    echo   cd ..
    echo   package.bat -debug
)

echo.
echo ========================================
echo    System Requirements
echo ========================================
echo.
echo Hardware:
echo - NVIDIA RTX GPU (RTX 20/30/40 series)
echo - DirectX 12 compatible system
echo.
echo Software:
echo - Windows 10/11 64-bit
echo - Latest NVIDIA drivers
echo - Visual Studio 2022 (for building)
echo - CMake 3.16+
echo.

echo ========================================
echo    Troubleshooting
echo ========================================
echo.
echo Common issues:
echo.
echo 1. "DLSS not supported":
echo    - Ensure you have an RTX GPU
echo    - Update NVIDIA drivers
echo    - Check GPU compatibility
echo.
echo 2. "DLL not found":
echo    - Run this setup script first
echo    - Check bin\x64 directory for required DLLs
echo    - Verify file paths and permissions
echo.
echo 3. "Build errors":
echo    - Ensure Visual Studio 2022 is installed
echo    - Check CMake version (3.16+ required)
echo    - Verify Windows SDK installation
echo.

echo Setup complete!
echo.
pause
